name: src

# load machine learning plugin
plugins:
  - pyPhasesRecordloaderMESA
  - pyPhasesRecordloaderSHHS
  - pyPhasesRecordloaderMrOS
  - pyPhasesRecordloader
  - SleepHarmonizer
  - pyPhasesML
  - SleePyPhases
  

importAfter:
  - configs/config.yml
  - local.yml
  - configs/datasets/tsm/loader.yaml
  # - configs/predefined.yml

phases:
  - name: Init
  - name: PreTraining
  - name: ModelAnalysis

data: []

config:
  data-path: ./data
  shhs-path: /data/cat/ws/freh129c-shhs/shhs
  mesa-path: /data/cat/ws/freh129c-shhs/mesa
  mros-path: /data/cat/ws/freh129c-shhs/mros
  sleepedf-path: /data/cat/ws/freh129c-shhs/sleepedf
  useLoader: shhs
  modelPath: src/models
  

  overview:
    # query: useLoader == 'tsm' and numLabels == 1 and `model.sleep` == False
    query: useLoader == 'tsm' and numLabels > 1 and `trainingParameter.batchSize` == 8
    dropColumns: [f1_Sleep,f1_Arousal,kappa_Arousal,eventCountDiff_Arousal,f1_LM,kappa_LM,eventCountDiff_LM]
    # and `classification.labelNames` == "['Sleep']"
    # ignoreKeys:
    #   - loader*
    # - dataset*
    # - dataBase
    # - dataBaseVersion
    # - filePath
    # - copyRawDataToLocal

  # Extract:
  #   useMultiThreading: False