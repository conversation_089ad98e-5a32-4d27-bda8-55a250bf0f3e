useLoader: mros
dataversion:
  version: Default
  seed: 2
  groupBy: patient
  recordIds: null
  # 3930: 3933 records - 3 missing annotaitons
  # 2905: 2908 patients - 3 missing annotaitons
  folds: 5
  split:
    trainval:
      - "727:2905"
    test: # ~25 % of patients
      - 0:727

model:
  multiclassificationSize: [4]
  
preprocessing:
  
  # stepsPerType:
  #   ppg: [filter, resample, clip, standardize]

  targetChannels:    
    - [A1]
    - [A2]
    - [C3]
    - [C4]
    - [ROC]
    - [LOC]
    - [L Chin]
    - [R Chin]
    - [Leg L]
    - [Leg R]
    - [Airflow]
    - [Thoracic]
    - [Abdominal]
    - [SaO2]

segmentManipulation:
- name: addBatchDimension
- name: selectYChannels
  channels: [2] # sleep, arousal, apnea, lm
- name: combineY  # [No Apnea, obstructive, mixed, central, hypopnea] -> [No Apnea, obstructive, mixed/central, hypopnea]
  values: [2,3]
- name: selectChannelRandom # select eeg channel
  channels: [0,1,2,3]
- name: selectChannelRandom # select eeg channel
  channels: [1, 2]
- name: selectChannelRandom # select eog channel (index is tailored)
  channels: [1, 2]
- name: changeType
  dtype: float32
- name: znorm
- name: MagScale
  low: 0.8
  high: 1.25
- name: prepareSingleEvent
  eventReduce: 64 # 1 per second
  position: left
  hours: 10
  samplingrate: 64 
  reduce: max # leg movements might be < 0.5 sec

inputShape: [2304000, 9]
