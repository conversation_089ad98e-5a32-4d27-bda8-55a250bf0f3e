preprocessing:
    targetChannels:
    - [EEG F3-A2]
    - [EEG F4-A1]
    - [EEG C3-A2]
    - [EEG C4-A1]
    - [EEG O1-A2, O1-M2, "O1:M2"]
    - [EEG O2-A1, O2-M1, "O2:M1"]
    - [EOG LOC-A2]
    - [EOG ROC-A1]
    - [<PERSON><PERSON> Chin]
oneHotDecoded: False

model:
  oneHotDecoded: False

segmentManipulation:
- name: addBatchDimension 
- name: changeType
  dtype: float32
- name: selectChannelRandom # select eeg channel
  channels: [0, 1, 2, 3, 4, 5]
- name: selectChannelRandom # select eog channel (index is tailored)
  channels: [1, 2]
- name: znorm
- name: MagScale
  low: 0.8
  high: 1.25
- name: fixedSize
  position: center
  size: 2097152 # 2^21 = 11,66h for 50Hz

inputShape: [2097152, 3]

# optimization:
useSourceChannels:
  - EEG F3-A2
  - EEG F4-A1
  - EEG C3-A2
  - EEG C4-A1
  - EEG O1-A2
  - EEG O2-A1
  - EOG LOC-A2
  - EOG ROC-A1
  - EMG Chin
  