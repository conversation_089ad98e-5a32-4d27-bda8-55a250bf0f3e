useSourceChannels: null

evalOn:
  useLoader: tsm
  datasetSplit: test

  useSourceChannels:
    - EEG F3-A2
    - EEG F4-A1
    - EEG C3-A2
    - EEG C4-A1
    - EEG O1-A2
    - EEG O2-A1
    - EOG LOC-A2
    - EOG ROC-A1
    - EMG Chin
  preprocessing:
    targetChannels:
    - [EEG F3-A2]
    - [EEG F4-A1]
    - [EEG C3-A2]
    - [EEG C4-A1]
    - [EEG O1-A2, O1-M2, "O1:M2"]
    - [EEG O2-A1, O2-M1, "O2:M1"]
    - [EOG LOC-A2]
    - [EOG ROC-A1]
    - [EMG Chin]

  segmentManipulationEval:
  - name: addBatchDimension 
  - name: changeType
    dtype: float32
  - name: selectChannels # select eeg channel
    channels: [2,7,8]
  - name: znorm
  - name: fixedSize
    position: center
    size: 2097152 # 2^21 = 11,66h for 50Hz

  dataversion:
    version: Default
    seed: 7
    groupBy: patient
    recordIds: null
    split:
      # 1707
      trainval:
        - 0:1100 # 
      test:
        - 1100:1707
    folds: 4
    minLength: 5 # in hours
    minimalSamplingRate:
      eeg: 200
      emg: 200
      mic: 200