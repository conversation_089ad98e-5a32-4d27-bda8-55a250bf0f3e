loader:
  tsm: &defaultsloader
    dataBase: TSM
    dataBaseVersion: 0.2.1 # needs to be raised every time new data is added

    filePath: data/TSM/
    copyRawDataToLocal: False
    dataIsFinal: True

    dataset:
      loaderName: RecordLoaderTSM
      fromFiles: False # if the records are read from files or sql-database
      filePattern: "*"
      downloader:
        basePath: /opt/records/
        canReadRemote: True
        type: allFromFolder
        listFilter: acq
        extensions: [.edf, .rml]
        force: False
        idPattern: .*/(.*[^-T]).edf

    # the channels that should be extracted from the edf files
    sourceChannels:
      - name: EEG F3-A2
        type: eeg
      - name: EEG F4-A1
        type: eeg
      - name: EEG C3-A2
        type: eeg
      - name: EEG C4-A1
        type: eeg
      - name: EEG O1-A2
        type: eeg
      - name: EEG O2-A1
        type: eeg
      - name: EEG A1-A2
        type: eeg
      - name: EOG LOC-A2
        type: eog
      - name: EOG ROC-A1
        type: eog
      - name: EMG Chin
        type: emg
      - name: Leg
        generated: True
        type: emg
      - name: Leg 1
        type: emg
      - name: Leg 2
        type: emg
      - name: Body
        type: body
      - name: Snore
        type: mic
      - name: Effort THO
        type: effort
      - name: Effort ABD
        type: effort
      - name: SpO2
        type: sao2
      - name: Flow Aux1
        type: flow
        optional: True
      - name: Flow Aux2
        type: flow
        optional: True
      - name: Flow Aux4
        type: flow
        optional: True
      - name: Flow Aux5
        type: flow
        optional: True
      - name: Flow Patient
        type: flow
        optional: True
      - name: FlowSelect
        generated: True
        requiresOne: [Flow Aux1, Flow Aux2, Flow Aux4, Flow Aux5, Flow Patient]
        type: flow
      - name: ECG I
        type: ecg
      - name: RR
        type: rr
      - name: Pleth
        type: ppg
        optional: True

    combineChannels:
      - name: Leg
        combineType: mean
        type: emg
        channel: [Leg 1, Leg 2]
      - name: FlowSelect
        combineType: select
        type: flow
        channel: [Flow Aux1, Flow Aux2, Flow Aux4, Flow Aux5, Flow Patient]
