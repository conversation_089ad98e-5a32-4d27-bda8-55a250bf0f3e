useLoader: tsm
dataversion:
  version: Default
  seed: 7
  groupBy: patient
  recordIds: null
  split:
    # 1707
    trainval:
      - 0:1100 # 0:1000
    test:
      - 1100:1707 # 0:1515
  folds: 5
  filterQuery: dataCount > 18000
  channelFilterQuery: type == 'eeg' and sample_frequency >= 200
  allChannels: "eeg"  # filter applies to all channels

preprocessing:
  stepsPerType:
    body: [positionAlice, resampleSimple]
  targetChannels:
    - [EEG F3-A2, F3-M2, EEG, "F3:M2"]         # 0 # 
    - [EEG F4-A1, F4-M1, EEG Ch2, "F4:M1"]     # 1 # 
    - [EEG C3-A2, C3-M2, "C3:M2"]              # 2 # 
    - [EEG C4-A1, C4-M1, "C4:M1"]              # 3 # 
    - [EEG O1-A2, O1-M2, "O1:M2"]              # 4 # 
    - [EEG O2-A1, O2-M1, "O2:M1"]              # 5 # 
    - [EOG LOC-A2, E1-M2, <PERSON><PERSON><PERSON>(L), "E1:M2"]     # 6 # 
    - [EOG ROC-A1, <PERSON><PERSON><PERSON>(R), "E2:M1"]            # 7 # 
    - [<PERSON><PERSON> Chin, Chin1-Chin2, <PERSON><PERSON>, <PERSON>]       # 8 # 
    - [Leg]                                    # 9 # 
    - [Body, POSITION]                         # 10 #
    - [Snore, Schnarch., SOUND]                # 11 #
    - [Effort THO, CHEST, RIP Thora, THOR RES] # 12 #
    - [Effort ABD, ABD, RIP Abdom, ABDO RES]   # 13 #
    - [SpO2, SaO2]                             # 14 #
    - [FlowSelect, FLOW, AIRFLOW]              # 15 #

inputShape: [2304000, 10]

segmentManipulation:
- name: addBatchDimension
- name: selectYChannels
  channels: [2] # sleep, arousal, apnea, lm
- name: selectChannelRandom
  channels: [0, 1, 2, 3, 4, 5]
- name: selectChannelRandom # select eeg channel
  channels: [1, 2]
- name: changeType
  dtype: float32
- name: znorm
- name: MagScale
  low: 0.8
  high: 1.25
- name: prepareSingleEvent
  eventReduce: 64 # 1 per second
  position: left
  hours: 10
  samplingrate: 64 
  reduce: max # leg movements might be < 0.5 sec

# segmentManipulationEval:
# - name: addBatchDimension
# - name: selectYChannels
#   channels: [2] # sleep, arousal, apnea, lm
# - name: selectChannelRandom
#   channels: [0, 1, 2, 3, 4, 5]
# - name: selectChannelRandom # select eeg channel
#   channels: [1, 2]
# - name: prepareSingleEvent
#   eventReduce: 64 # 1 per second
#   position: left
#   hours: 10
#   samplingrate: 64 
#   reduce: max # leg movements might be < 0.5 sec

# Extract:
#   useMultiThreading: False
trainingParameter:
  # batchSize: 3
  batchSize: 8