segmentManipulation:
- name: addBatchDimension
- name: selectYChannels
  channels: [0] # sleep, arousal, apnea, lm
- name: selectChannelRandom
  channels: [0, 1, 2, 3, 4, 5]
- name: selectChannelRandom # select eeg channel
  channels: [1, 2]
- name: changeType
  dtype: float32
- name: znorm
- name: MagScale
  low: 0.8
  high: 1.25
- name: prepareSingleSleep
  sleepReduce: 1920 # 1 per 30 seconds
  eventReduce: 64 # 1 per second
  position: left
  hours: 10
  samplingrate: 64 
  reduce: mean
model:
  sleep: true
  events: false
  multiclassificationSize: [5]

trainingParameter:
  validationMetrics:
    - [kappa, f1]


thresholdMetric: [f1]
    
classification:
  labelNames: [Sleep]
  scorerTypes: [segment]
  classNames:
    - [Wake, R, N1, N2, N3]
  classes: 
    - [Wake, R, N1, N2, N3]
    
  predictionSignals: [Sleep]
  predictionFrequencies: [ 1 ]
  numClasses: [5]
