# last change drops early perferomance
# - removed focal loss
# - removed se_reduction
# debug -changes
# useSourceChannels to exclude less recordings
# use predefined recordIds

# Patients from MESA were randomly allocated to mutually exclusive train and tests sets, stratifying by age, gender, and apnea-hypopnea index (AHI). 

# ✅ MESA-train contains 1,850 patients and MESA-test 204 patients

# We applied TL to our external database using 4-folds. The pretrained model was used as a starting point and each fold was trained and evaluated independently, before all results were brought together and analyzed as a whole.

useLoader: mesa
dataversion:
  version: ppgnet-split
  seed: null
  groupBy: null
  folds: 4
  trainval_seed: 2025
  recordIds: null
  split:
    # 2056 unqiue records
    # trainval: 0:1850
    # test: 1850:2056
    # optional use test recordIds from the supplements
    test: 0:204
    trainval: "204:2056"
  # we use the records provided in the supplements listed in config/predefined.yml

model:
  multiclassificationSize: [4, 1, 4, 1]
  sleep: true
  events: true

  lossOptions:
    lossReduce: mean
    binLoss: bcelogits
    catLoss: crossentropy
    # normalize: logsoftmax
    normalize: null

  # bin
  # implementation: wav2sleep
  # loss: TemporalBalanceCrossEntropyLoss
  # loss: FocalTemporalBalanceCrossEntropyLoss
#   pretrained: false
  dilations: [1, 2, 4, 8, 16, 32]
  se_reduction: null
  sleep_dense_out: 128
  event_dense_out: 240

  micro_window_size: 1  # 1-second windows for micro events
  base_window_size: 30  # 30-second windows for sleep stages
  task_weights:
    sleep_stages: 1.0
    micro_events.arousal: 1.0
    micro_events.leg_movement: 1.0
    micro_events.respiratory: 1.0

  
  se_reduction: 16
  event_tcn_delations: [1, 2, 4, 8, 16, 32]

  
  reduceLROnPlateau:
    monitor: auprc
    patience: 3
    factor: 0.1


labelChannels:
  - SleepStagesAASM
  - SleepArousals
  - SleepApnea
  - SleepLegMovements
    
preprocessing:
  targetFrequency: 64 # 2048 samples per epoch
  labelFrequency:  64 # 30s epochs
  # targetFrequency: 68.26666666667 # 2048 samples per epoch
  # labelFrequency:  68.26666666667 # 30s epochs

  manipulationSteps: []
  stepsPerType:
    ppg: [filter, resample, clip, standardize]

  targetChannels:
    - [Pleth]


recordWise: True

# ✅ probability prediction for each of the 4 sleep stages
# ✅ constant length of 10 hours Truncation or zero padding was only applied to the end of each recording
ignoreClassIndex: -1

segmentManipulation:
- name: addBatchDimension
# - name: selectYChannels
#   channels: [0] # sleep, arousal, apnea, lm
- name: changeType
  dtype: float32
- name: toLightDeepSleep  # [Wake, R, N1, N2, N3] ->  [Wake, R, Light, Deep]
  channel: 0
- name: combineY  # [No Apnea, obstructive, mixed, central, hypopnea] -> [No Apnea, obstructive/mixed, central, hypopnea]
  values: [1,2]
  channel: 2
# - name: znorm
# - name: MagScale
#   low: 0.8
#   high: 1.25
- name: prepareModelInput
  sleepReduce: 1920 # 1 per 30 seconds
  eventReduce: 64 # 1 per second
  position: left
  hours: 10
  samplingrate: 64 
  reduce: mean

# - name: fixedSizeX
#   position: left
#   fillValue: 0
#   size: 2304000 # fs * 60 * 60 * 10 ~= 10 h
# - name: reduceY
#   # factor: 64 # 1 per second
#   factor: 1920 # 1 per 30 second
# - name: fixedSizeY
#   fillValue: -1
#   position: left
#   # size: 36000 # 10 hour for 1 Hz
#   size: 1200 # 10 hour for 1 Hz

inputShape: [2304000, 1]

batchManipulation:
  - name: prepareBatch

manipulationAfterPredict:
  - name: to_numpy
  - name: transposeX
  - name: deleteIgnored
  # - name: toLightDeepSleep
  # - name: flattenBatch
  # - name: hotDecode
    # xChannel: 0
  # - name: stack

  # - name: transposeToLabels
  # - name: merge
  # - name: hotDecode
    # xChannel: 0

# useSourceChannels:
# - Pleth
  
# ✅ Loss was calculated using the categorical cross-entropy loss
# ✅ Temporal sample weighting was used to address class imbalance 
# ✅ remove padded regions from loss calculations. 
# ✅ The Adam optimizer was used.
# ✅ When training SleepPPG-Net models from scratch, we used a learning rate of 2.5 × 10−4 and trained for 30 epochs.
# ✅ A batch size of 8 was used in all experiments
trainingParameter:
  # stopAfterNotImproving: 0
  # maxEpochs: 30
  
  # learningRate: 0.04
  learningRate: 0.00025
  # learningRateDecay: 0
  # learningRateDecay: 0.001
  # learningRateDecay: 0.0001
  batchSize: 8
  cyclicLearningRateCustom: False
  cyclicLearningRate: False
  optimizer: adams
  classWeights: null
  shuffle: True
  shuffleSeed: 2025

  # test to run longer
  stopAfterNotImproving: 25
  maxEpochs: 1000

  validationMetrics:
    - [f1, kappa]
    - [auprc, f1, kappa, eventCountDiff]
    - [f1, kappa]
    - [auprc, f1, kappa, eventCountDiff]

modelName: SleepPPGNet
# ✅ Initial weights for convolutional neural network, DNN, and LSTM layers were set with Xavier uniform initialization.

debugConfig:
  trainingParameter:
    stopAfterNotImproving: 1
    batchSize: 2
    maxEpochs: 2
  validatDataset: false
  dataversion:
    version: Debug-split
    recordIds:
      - mesa-sleep-1294
      - mesa-sleep-6176
      - mesa-sleep-2993
      # - mesa-sleep-6072
    split:
      trainval: 0:2
      test: "2:3"


classification:
  name: sleepStage
  type: classification
  annotation: SleepStagesAASM
  labelNames: 
    - Sleep
    - Arousal
    - Apnea
    - LM
  classNames: 
    - [Wake, R, Light, Deep]
    - [No Arousal, Arousal]
    - [No Apnea, obstructive/mixed, central, hypopnea]
    - [No LM, Legmovement]
  
  

  # classNames: [Stage]
  # classNames: [Stage, Arousal, Apnea, LM]
  # scorerTypes: [segment]
  scorerTypes: [segment, event, event, event]
  classes: 
    # - [Wake, R, N1, N2, N3]
    - [Wake, R, N1, Light, Deep]
  predictionSignals: [Sleep4, "Arousal", "Apnea4", "LM"]
  predictionFrequencies: [0.03333, 1, 1, 1]

eval:
  batchSize: 1
  metrics:
    - [f1, kappa]
  clinicalMetrics:
    - tst
    - waso
    - sLatency
    - rLatency

fixedThreshold: [0.5] # skips optimization

endFold: 1