model:
  downsampling_channels: [1, 16, 16, 32, 32, 64, 128, 256]


useLoader: mesa
dataversion:
  version: ppgnet-split
  seed: null
  groupBy: null
  folds: 4
  trainval_seed: 2025
  recordIds: null
  split:
    # 2056 unqiue records
    # trainval: 0:1850
    # test: 1850:2056
    # optional use test recordIds from the supplements
    test: "1100:2070"
    trainval: "0:1100"

preprocessing:
  targetFrequency: 32 # 2048 samples per epoch
  labelFrequency:  32 # 30s epochs
  # targetFrequency: 68.26666666667 # 2048 samples per epoch
  # labelFrequency:  68.26666666667 # 30s epochs

  manipulationSteps:
  - name: trimToLightAndScoring

  stepsPerType:
    # ppg: [filter, resample, clip, standardize]
    # body: [positionSHHS, resampleSimple]
    # eeg: [resampleFIR, fftConvolution18m, standardisation]
    # eog: [resampleFIR, fftConvolution18m, standardisation]
    # emg: [resampleFIR, fftConvolution18m, standardisation]
    
    body: [positionMESA]
    mic: [fftConvolution18m, standardisation]
    effort: [fftConvolution18m, standardisation]
    Leg:  [fftConvolution18m, standardisation]
    sao2:
      - name: resampleSimple
        targetFrequency: 32
      - name: normalizePercentage70
      - name: standardisation
  #   flow: [resample, standardisation, fftConvolution18m]
  # # stepsPerType:
  # #   ppg: [filter, resample, clip, standardize]
  # #   eeg: [filter, resample, clip, standardize]
  # #   eog: [filter, resample, clip, standardize]
  # #   emg: [filter, resample, clip, standardize]
  # #   body: [positionMESA, resampleSimple]
  # #   mic: [filter, resample, clip, standardize]
  # #   effort: [resample, clip, standardize]
  # #   sao2: [resampleSimple, clip, standardize]
  # #   flow: [resample, clip, standardize]

  targetChannels:
    # - [EEG1]
    # - [EEG2]
    # - [EEG3]
    # - [EOG-L]
    # - [EOG-R]
    # - [EMG]
    - [Leg]
    - [Thor]
    - [Abdo]
    - [SpO2]
    - [Flow]
    - [Pres]
    - [FLOW_THERM]
    - [Snore]

inputShape: [2304000, 8]