preprocessing:
  targetFrequency: 64 # 2048 samples per epoch
  labelFrequency:  64 # 30s epochs
  # targetFrequency: 68.26666666667 # 2048 samples per epoch
  # labelFrequency:  68.26666666667 # 30s epochs

  manipulationSteps:
  - name: trimToLightAndScoring

  # stepsPerType:
  #   # ppg: [filter, resample, clip, standardize]
  #   # body: [positionSHHS, resampleSimple]
  #   eeg: [resampleFIR, standardisation, fftConvolution18m]
  #   eog: [resampleFIR, standardisation, fftConvolution18m]
  #   emg: [resampleFIR, standardisation, fftConvolution18m]
  #   body: [positionMESA, resampleSimple]
  #   mic: [resampleFIR, standardisation, fftConvolution18m]
  #   effort: [resample, standardisation, fftConvolution18m]
  #   sao2: [resampleSimple, normalizePercentage70, standardisation]
  #   flow: [resample, standardisation, fftConvolution18m]
  # stepsPerType:
  #   ppg: [filter, resample, clip, standardize]
  #   eeg: [filter, resample, clip, standardize]
  #   eog: [filter, resample, clip, standardize]
  #   emg: [filter, resample, clip, standardize]
  #   leg: [filter, resample, clip, standardize]
  #   body: [positionMESA, resampleSimple]
  #   mic: [filter, resample, clip, standardize]
  #   effort: [filter, resample, clip, standardize]
  #   sao2: [resampleSimple, clip, standardize]
  #   flow: [filter, resample, clip, standardize]
  stepsPerType:

    
    # High-frequency signals (e.g., 256 Hz -> 64 Hz)
    eeg:
      - name: highpass
        cutoff: 0.3 # Hz
        order: 4
      - name: notch
        freq: 50.0 # Hz (adjust to 60.0 if needed)
        quality_factor: 30.0
      - name: lowpass_antialias # Crucial before downsampling
        cutoff: 28.8 # Target 64Hz * 0.45 (well below Nyquist of 32Hz for 64Hz target)
        order: 8
      - name: resample_poly
        target_freq: 64.0
      - name: clip
      - name: standardize

    eog:
      - name: highpass
        cutoff: 0.1 # Hz
        order: 4
      - name: notch
        freq: 50.0 # Hz
        quality_factor: 30.0
      - name: lowpass_antialias
        cutoff: 28.8 # Hz
        order: 8
      - name: resample_poly
        target_freq: 64.0
      - name: clip
      - name: standardize

    emg: # For chin EMG (not envelope)
      - name: highpass
        cutoff: 10.0 # Hz
        order: 4
      - name: notch
        freq: 50.0 # Hz
        quality_factor: 30.0
      - name: lowpass_antialias # This will limit EMG frequency content
        cutoff: 28.8 # Hz
        order: 8
      - name: resample_poly
        target_freq: 64.0
      - name: clip
      - name: standardize

    # Medium/Low-frequency signals (e.g., 32 Hz -> 64 Hz, upsampling)
    Leg: 
      - name: highpass
        cutoff: 5.0 # Hz
        order: 4
      # Notch might not be strictly needed if original 32Hz signal is clean or its Nyquist (16Hz) is below powerline.
      # If needed, add notch step here.
      - name: resample_poly # Upsampling
        target_freq: 64.0
      - name: lowpass_smooth # Smooth after upsampling, keep relevant frequencies
        cutoff: 15.0 # Hz (original Nyquist was 16Hz, don't introduce spurious high freqs)
        order: 4
      - name: clip
      - name: standardize

    effort: # Thoracic effort
      - name: highpass
        cutoff: 0.05 # Hz, remove very slow drift
        order: 2
      - name: resample_poly # Upsampling
        target_freq: 64.0
      - name: lowpass_smooth # Respiratory signals are slow
        cutoff: 5.0 # Hz
        order: 4
      - name: clip
      - name: standardize

    flow: # Generic Airflow (e.g., from pneumotach)
      - name: highpass # Or your fft_detrend_long if that's a named step
        cutoff: 0.05 # Hz
        order: 2
      # Or:
      # - name: fft_detrend_long # If this specific method exists
      #   kernel_seconds: 900 # 15 minutes
      - name: resample_poly # Upsampling
        target_freq: 64.0
      - name: lowpass_smooth # Flow can contain snoring, allow slightly higher freqs
        cutoff: 10.0 # Hz
        order: 4
      - name: clip
      - name: standardize

    # Very low-frequency signal (e.g., 1 Hz -> 64 Hz)
    spo2:
      - name: resample_interpolate # Specific resampler for slow signals
        target_freq: 64.0
        kind: 'linear' # Interpolation kind
      - name: lowpass_smooth # Smooth to reflect original signal bandwidth
        cutoff: 0.45 # Hz (original Nyquist was 0.5Hz)
        order: 2
      - name: clip_fixed
        min_val: 50 # %
        max_val: 100 # %
      - name: standardize
      
    mic: # Snore signal from microphone, originally 32 Hz
    - name: highpass
      cutoff: 1.0 # Hz, Remove DC and very slow pressure changes/drift.
                  # Snores are transient sound events, so DC isn't relevant.
                  # Don't set too high, as main snore energy at 32Hz Fs will be low.
      order: 4
    # No notch filter needed: Original Nyquist is 16 Hz, well below 50/60 Hz powerline.
    - name: resample_poly # Upsampling from 32 Hz to 64 Hz
      target_freq: 64.0
    - name: lowpass_smooth # Smooth after upsampling.
      # The original signal was bandlimited to 16 Hz.
      # This filter ensures we don't have sharp artifacts from upsampling
      # and respect the original bandwidth.
      cutoff: 15.0 # Hz (just below the original Nyquist of 16Hz)
      order: 4
    - name: clip
    - name: standardize # Z-score normalization.