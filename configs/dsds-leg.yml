segmentManipulation:
- name: addBatchDimension
- name: selectYChannels
  channels: [3] # sleep, arousal, apnea, lm
- name: selectChannelRandom
  channels: [0, 1, 2, 3, 4, 5]
- name: selectChannelRandom # select eeg channel
  channels: [1, 2]
- name: changeType
  dtype: float32
- name: znorm
- name: MagScale
  low: 0.8
  high: 1.25
- name: prepareSingleEvent
  eventReduce: 64 # 1 per second
  position: left
  hours: 10
  samplingrate: 64 
  reduce: max # leg movements might be 0.5 sec

model:
  multiclassificationSize: [1]

trainingParameter:
  validationMetrics:
    - [auprc, f1, kappa, eventCountDiff]


thresholdMetric: [f1]
    
classification:
  labelNames: [LM]
  scorerTypes: [event]
  classNames:
    - [No LM, LM]
  classes: 
    - [No LM, LM]
    
  predictionSignals: [LMBin]
  predictionFrequencies: [ 1 ]
  numClasses: [1]
