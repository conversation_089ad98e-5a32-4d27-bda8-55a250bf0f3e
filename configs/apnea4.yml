# last changes:
# se 16 -> 16
# more tcn delations
# torch.set_float32_matmul_precision('high')

model:
  multiclassificationSize: [4]
  sleep: false
  lossOptions:
    lossReduce: mean
    binLoss: bce-focal
    catLoss: cat
    dice_smooth: 1.0
    label_smoothing: 0.2
    # class_weights: [0.1, 1.0, 5.0, 5.0, 1.0]
    class_weights: null
    focalGamma: 4
  se_reduction: 16
  event_tcn_delations: [1, 2, 4, 8, 16, 32]

recordWise: True

# ✅ probability prediction for each of the 4 sleep stages
# ✅ constant length of 10 hours Truncation or zero padding was only applied to the end of each recording
ignoreClassIndex: -1

segmentManipulation:
- name: addBatchDimension
- name: selectYChannels
  channels: [2] # sleep, arousal, apnea, lm
- name: combineY  # [No Apnea, obstructive, mixed, central, hypopnea] -> [No Apnea, obstructive, mixed/central, hypopnea]
  values: [2,3]
  channel: 0
# - name: selectChannelRandom # select eeg channel
#   channels: [0, 1, 2]
# - name: selectChannelRandom # select eog channel (index is tailored)
#   channels: [1, 2]
- name: changeType
  dtype: float32
- name: znorm
- name: MagScale
  low: 0.8
  high: 1.25
- name: prepareSingleEvent
  eventReduce: 32 # 1 per second
  position: left
  hours: 10
  samplingrate: 32 
  reduce: max # leg movements might be < 0.5 sec

segmentManipulationEval:
- name: addBatchDimension
- name: selectYChannels
  channels: [2] # sleep, arousal, apnea, lm
- name: combineY  # [No Apnea, obstructive, mixed, central, hypopnea] -> [No Apnea, obstructive, mixed/central, hypopnea]
  values: [2,3]
  channel: 0
# - name: selectChannels # select eeg channel
#   channels: [0, 3, 5, 6, 7, 8, 9, 10, 11, 12]
- name: changeType
  dtype: float32
- name: prepareSingleEvent
  eventReduce: 32 # 1 per second
  position: left
  hours: 10
  samplingrate: 32 
  reduce: max # leg movements might be < 0.5 sec

inputShape: [1152000, 8]

trainingParameter:
  validationMetrics:
    - [f1, kappa]
  # learningRate: 0.00025
  batchSize: 8
  # learningRate: 0.0035
  # findCyclicLearningRate: True
  cyclicLearningRateCustom: True
  cyclicLearningRate: True
  learningRate: 0.025
  # cyclicLearningRateCustom: False


classification:
  labelNames: [Apnea]
  scorerTypes: [event]
  classNames:
    - [No Resp. Event, obstructive, mixed-central, hypopnea]
  classes: 
    - [No Resp. Event, obstructive, mixed-central, hypopnea]
    
  predictionSignals: [Apnea4]
  predictionFrequencies: [ 2 ]
  numClasses: [4]

eval:
  batchSize: 1
  metrics:
    - [auprc]
  clinicalMetrics: []

fixedThreshold: [0.5] # skips optimization

endFold: 1