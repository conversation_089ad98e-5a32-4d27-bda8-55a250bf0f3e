# last changes:
# se 16 -> 16
# more tcn delations
# torch.set_float32_matmul_precision('high')

model:
  multiclassificationSize: [5]
  sleep: false
  lossOptions:
    lossReduce: mean
    binLoss: bcelogits
    catLoss: cat
    dice_smooth: 1.0
    # label_smoothing: 0.2
    class_weights: null
    # class_weights: [0.1, 1.0, 5.0, 5.0, 1.0]
    focalGamma: 4
  se_reduction: 16
  event_tcn_delations: [1, 2, 4, 8, 16, 32]

recordWise: True

# ✅ probability prediction for each of the 4 sleep stages
# ✅ constant length of 10 hours Truncation or zero padding was only applied to the end of each recording
ignoreClassIndex: -1

segmentManipulation:
- name: addBatchDimension
- name: ignoreWakeSegments
  channel: 2
- name: selectYChannels
  channels: [2] # sleep, arousal, apnea, lm
- name: changeType
  dtype: float32
- name: znorm
- name: MagScale
  low: 0.8
  high: 1.25
- name: prepareSingleEvent
  eventReduce: 64 # 1 per second
  position: left
  hours: 10
  samplingrate: 64 
  reduce: max # leg movements might be < 0.5 sec

trainingParameter:
  validationMetrics:
    - [f1, kappa]
  learningRate: 0.00025
  batchSize: 8
  # learningRate: 0.0035
  # findCyclicLearningRate: True
  # cyclicLearningRateCustom: True
  # cyclicLearningRate: True
  # cyclicLearningRateCustom: False


classification:
  labelNames: [Apnea]
  scorerTypes: [event]
  classNames:
    - [No Resp. Event, obstructive, mixed, central, hypopnea]
  classes: 
    - [No Resp. Event, obstructive, mixed, central, hypopnea]
    
  predictionSignals: [Apnea]
  predictionFrequencies: [ 2 ]
  numClasses: [5]

eval:
  batchSize: 1
  metrics:
    - [auprc]
  clinicalMetrics: []

fixedThreshold: [0.5] # skips optimization

endFold: 1

# {"targetFrequency": 64, "labelFrequency": 64, "manipulationSteps": [{"name": "trimToLightAndScoring"}], "stepsPerType": {"ppg": ["filter", "resample", "clip", "standardize"], "eeg": ["resampleFIR", "standardisation", "fftConvolution18m"], "eog": ["resampleFIR", "standardisation", "fftConvolution18m"], "emg": ["resampleFIR", "standardisation", "fftConvolution18m"], "body": ["positionAlice", "resampleSimple"], "mic": ["resampleFIR", "standardisation", "fftConvolution18m"], "effort": ["resample", "standardisation", "fftConvolution18m"], "sao2": ["resample_interpolate", "normalizePercentage70", "standardisation"], "flow": ["resample", "standardisation", "fftConvolution18m"]}, "targetChannels": [["EEG F3-A2", "F3-M2", "EEG", "F3:M2"], ["EEG F4-A1", "F4-M1", "EEG Ch2", "F4:M1"], ["EEG C3-A2", "C3-M2", "C3:M2"], ["EEG C4-A1", "C4-M1", "C4:M1"], ["EEG O1-A2", "O1-M2", "O1:M2"], ["EEG O2-A1", "O2-M1", "O2:M1"], ["EOG LOC-A2", "E1-M2", "EOG(L)", "E1:M2"], ["EOG ROC-A1", "EOG(R)", "E2:M1"], ["EMG Chin", "Chin1-Chin2", "EMG", "Chin"], ["Leg"], ["Body", "POSITION"], ["Snore", "Schnarch.", "SOUND"], ["Effort THO", "CHEST", "RIP Thora", "THOR RES"], ["Effort ABD", "ABD", "RIP Abdom", "ABDO RES"], ["SpO2", "SaO2"], ["FlowSelect", "FLOW", "AIRFLOW"]], "forceGapBetweenEvents": false, "featureChannels": []}
# {'targetFrequency': 64, 'labelFrequency': 64, 'manipulationSteps': [{'name': 'trimToLightAndScoring'}], 'stepsPerType': {'ppg': ['filter', 'resample', 'clip', 'standardize'], 'eeg': ['resampleFIR', 'standardisation', 'fftConvolution18m'], 'eog': ['resampleFIR', 'standardisation', 'fftConvolution18m'], 'emg': ['resampleFIR', 'standardisation', 'fftConvolution18m'], 'body': ['positionAlice', 'resampleSimple'], 'mic': ['resampleFIR', 'standardisation', 'fftConvolution18m'], 'effort': ['resample', 'standardisation', 'fftConvolution18m'], 'sao2': ['resample_interpolate', 'normalizePercentage70', 'standardisation'], 'flow': ['resample', 'standardisation', 'fftConvolution18m']}, 'targetChannels': [['EEG F3-A2', 'F3-M2', 'EEG', 'F3:M2'], ['EEG F4-A1', 'F4-M1', 'EEG Ch2', 'F4:M1'], ['EEG C3-A2', 'C3-M2', 'C3:M2'], ['EEG C4-A1', 'C4-M1', 'C4:M1'], ['EEG O1-A2', 'O1-M2', 'O1:M2'], ['EEG O2-A1', 'O2-M1', 'O2:M1'], ['EOG LOC-A2', 'E1-M2', 'EOG(L)', 'E1:M2'], ['EOG ROC-A1', 'EOG(R)', 'E2:M1'], ['EMG Chin', 'Chin1-Chin2', 'EMG', 'Chin'], ['Leg'], ['Body', 'POSITION'], ['Snore', 'Schnarch.', 'SOUND'], ['Effort THO', 'CHEST', 'RIP Thora', 'THOR RES'], ['Effort ABD', 'ABD', 'RIP Abdom', 'ABDO RES'], ['SpO2', 'SaO2'], ['FlowSelect', 'FLOW', 'AIRFLOW']], 'forceGapBetweenEvents': False, 'featureChannels': []}