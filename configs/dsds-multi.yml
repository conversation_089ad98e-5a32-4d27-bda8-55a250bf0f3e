segmentManipulation:
- name: addBatchDimension
# - name: selectYChannels
#   channels: [1] # sleep, arousal, apnea, lm
- name: selectChannelRandom
  channels: [0, 1, 2, 3, 4, 5]
- name: selectChannelRandom # select eeg channel
  channels: [1, 2]
- name: changeType
  dtype: float32
- name: znorm
- name: MagScale
  low: 0.8
  high: 1.25
- name: prepareModelInput
  sleepReduce: 1920 # 1 per 30 seconds
  eventReduce: 64 # 1 per second
  position: left
  hours: 10
  samplingrate: 64 
  reduce: max # leg movements might be 0.5 sec

model:
  sleep: true
  multiclassificationSize: [5, 1, 5, 1]

  # downsampling_channels: [1, 16, 32, 64, 128, 128, 256, 256, 512]
  downsampling_channels: [1, 32, 32, 64, 64, 128, 128, 256, 256]
  event_tcn_delations: [1, 2, 4, 8, 16]
  sleep_tcn_delations: [1, 2, 4, 8]
  sleep_dense_out: 180
  event_dense_out: 240
  # fe_kernel: 5

  lossOptions:
    lossReduce: uncertainty
    # hard_negative_mining: True
    # hard_negative_strategy: topk
    # hard_negative_ratio: 5
    # hard_negative_min_keep: 1000
    
  reduceLROnPlateau:
    monitor:
      - kappa_Sleep
      - auprc_Arousal
      - kappa_Apnea
      - f1_Apnea
      - auprc_LM
    patience: 3
    factor: 0.1

trainingParameter:
  validationMetrics:
    - [kappa, f1]
    - [auprc, f1, kappa, eventCountDiff]
    - [kappa, f1]
    - [auprc, f1, kappa, eventCountDiff]


thresholdMetric: [kappa, f1, f1, f1]
    
classification:
  labelNames: [Sleep, Arousal, Apnea, LM]
  scorerTypes: [segment, event, event, event]
  classNames:
    - [Wake, R, N1, N2, N3]
    - [No Arousal, Arousal]
    - [No Resp. Event, obstructive, mixed, central, hypopnea]
    - [No LM, LM]
  classes: 
    - [Wake, R, N1, N2, N3]
    - [No Arousal, Arousal]
    - [No Resp. Event, obstructive, mixed, central, hypopnea]
    - [No LM, LM]
    
  predictionSignals: [Sleep, Arousal, Apnea, LM]
  predictionFrequencies: [ 1, 1, 1, 1 ]
  numClasses: [5, 1, 5, 1]
