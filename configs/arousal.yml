# last changes:
# se 16 -> 16
# more tcn delations
# torch.set_float32_matmul_precision('high')

model:
  multiclassificationSize: [1]
  sleep: false
  lossOptions:
    lossReduce: mean
    binLoss: bce-focal
  se_reduction: 16
  event_tcn_delations: [1, 2, 4, 8, 16, 32]

recordWise: True

# ✅ probability prediction for each of the 4 sleep stages
# ✅ constant length of 10 hours Truncation or zero padding was only applied to the end of each recording
ignoreClassIndex: -1

segmentManipulation:
- name: addBatchDimension
- name: selectYChannels
  channels: [1] # sleep, arousal, apnea, lm
- name: changeType
  dtype: float32
- name: znorm
- name: MagScale
  low: 0.8
  high: 1.25
- name: prepareSingleEvent
  eventReduce: 64 # 1 per second
  position: left
  hours: 10
  samplingrate: 64 
  reduce: max # leg movements might be < 0.5 sec

trainingParameter:
  validationMetrics:
    - [auprc, f1, kappa, eventCountDiff]
  learningRate: 0.00025
  batchSize: 8
  # learningRate: 0.0035
  # findCyclicLearningRate: True
  # cyclicLearningRateCustom: True
  # cyclicLearningRate: True
  # cyclicLearningRateCustom: False

classification:
  labelNames: [Arousal]
  classNames: 
    - [No Arousal, Arousal]
  scorerTypes: [event]

  classes: 
    - [No Arousal, Arousal]
  predictionSignals: ["Arousal"]
  predictionFrequencies: [1]

eval:
  batchSize: 1
  metrics:
    - [auprc]
  clinicalMetrics: []

fixedThreshold: [0.5] # skips optimization

endFold: 1