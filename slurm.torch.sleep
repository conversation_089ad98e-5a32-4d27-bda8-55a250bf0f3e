#!/bin/bash

#Submit this script with: sbatch slurm.torch
# Submit this script with: sbatch /projects/p_sleepingbeauty/physionet-challenge-2023/slurm.torch

#SBATCH --time=12:00:00   # walltime
#SBATCH --nodes=1   # number of nodes
#SBATCH --mincpus=1
#SBATCH --gres=gpu:1
#SBATCH --ntasks=1      # limit to one node
#SBATCH --cpus-per-task=8  # number of processor cores (i.e. threads)
#SBATCH --partition=capella
#SBATCH --mem-per-cpu=16000M   # memory per CPU core
#SBATCH -J "spp"   # job name
#SBATCH --mail-user=<EMAIL>   # email address
#SBATCH --mail-type=BEGIN,END,FAIL,REQUEUE,TIME_LIMIT,TIME_LIMIT_90
#SBATCH -A p_sleepingbeauty
#SBATCH --output=logs/simulation-m-%j.out
#SBATCH --error=logs/simulation-m-%j.err

# srun --pty --nodes=1 --ntasks=1 --time=06:00:00 --partition alpha --gres=gpu:1 --cpus-per-task=1 --mem=32G bash -l
# sattach

# Set the max number of threads to use for programs using OpenMP. Should be <= ppn. Does nothing if the program doesn't use OpenMP.
export OMP_NUM_THREADS=$SLURM_CPUS_ON_NODE

# module load modenv/ml PyTorch
module load release/24.04  GCC/12.3.0  OpenMPI/4.1.5 PyTorch/2.1.2-CUDA-12.1.1
source /home/<USER>/project/sleepyphases/spp/spp/bin/activate

function join_by() {
    local d=$1
    shift
    local f=$1
    shift
    printf %s "$f" "${@/#/$d}"
}

export PROJECTPATH=/projects/p_sleepingbeauty/SPP-PPGNet/
# optional use a grid file
# export GRID="-g $CONFIGPATH/grid.drcnntrans.yaml -r --csv gridearch.drcnntrans.csv"

python -m pip install -U --user -r $PROJECTPATH/requirements.txt


# cd $PROJECTPATH
echo "RUN: python -m phases run -p $PROJECTPATH/project.yaml -o $PROJECTPATH/ $@"
python -m phases run $@

# python -m phases run -p /projects/p_sleepingbeauty/sleepingbeauty/project.yaml -c /projects/p_sleepingbeauty/sleepingbeauty/userconfigs/local.yaml -o /projects/p_sleepingbeauty/sleepingbeauty/ FeatureSleep

exit 0
