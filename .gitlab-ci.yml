image: docker:20.10.21
services:
  - name: docker:20.10.21-dind
    alias: docker

variables:
  PROJECTNAME: $CI_PROJECT_NAME
  RELEASETAG: $CI_COMMIT_SHORT_SHA
  DOCKER_TLS_CERTDIR: "/certs"
  CONTAINER_IMAGES: $CI_REGISTRY_IMAGE
  DEPLOY_TARGETS: prod
  # CONTAINER_IMAGES: $CI_REGISTRY_IMAGE $CI_REGISTRY_IMAGE/tf
  # DEPLOY_TARGETS: prod prod-tensorflow
  DOCKER_BUILD_CACHE: /cache/docker

stages:
  - release
  - publish

before_script:
  - docker info
  - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  - '[ "$CI_COMMIT_TAG" != "" ] && export RELEASETAG=$CI_COMMIT_TAG'
  - '[ "$VERSION" = "" ] && VERSION=0.0.1'
  - CURRENT_VERSION=$VERSION
  - '[ "$CI_COMMIT_TAG" = "" ] && VERSION=$VERSION-rc$RELEASETAG'
  - echo The current Version is $VERSION
  - '[ "$CI_COMMIT_REF_NAME" != "master" ] && export VERSION=$VERSION-branch-$CI_COMMIT_REF_NAME'
  - '[ "$CI_COMMIT_TAG" != "" ] && export VERSION=$CI_COMMIT_TAG'
  - echo the new VERSION IS $VERSION

docker-releaseimage:
  stage: release
  except:
    - tags
  script:
    - export images="$CONTAINER_IMAGES"
    - export tags="latest $CI_COMMIT_SHORT_SHA"
    - |
      i=1
      for image in $images
      do
        target=$(echo $DEPLOY_TARGETS | cut -d' ' -f$i)
        echo "Build $target for $image"
        docker build --cache-from $CI_REGISTRY/$CI_PROJECT_NAME/$image:latest  --build-arg VERSION=$RELEASETAG --target $target -t $image .
        for tag in $tags
        do
          docker tag $image:latest $image:$tag
          docker push $image:$tag
        done
        i=$((i+1))
      done

docker-releaseimage-production:
  stage: publish
  only:
    - tags
  script:
    - export images="$CONTAINER_IMAGES"
    - export tags="stable $RELEASETAG"
    - |
      i=1
      for image in $images
      do
        target=$(echo $DEPLOY_TARGETS | cut -d' ' -f$i)
        docker pull $image:$CI_COMMIT_SHORT_SHA
        for tag in $tags
        do
          docker tag $image:$CI_COMMIT_SHORT_SHA $image:$tag
          docker push $image:$tag
        done
        i=$((i+1))
      done
