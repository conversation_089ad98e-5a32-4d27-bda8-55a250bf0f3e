services:

  app:
    # image: registry.gitlab.com/sleep-is-all-you-need/reproduce/spp-sleeptranformer:16fb67cf
    # docker compose run --rm -d --entrypoint bash app
    build: 
      context: .
      target: prod
      args:
      - http_proxy=http://ukd-proxy.med.tu-dresden.de:80
      - https_proxy=http://ukd-proxy.med.tu-dresden.de:80
      - no_proxy=localhost,127.0.0.1,::1,med.tu-dresden.de,uniklinikum-dresden.de,ukdd.de
    entrypoint: python -m phases
    ipc: host
    volumes:
      - ./src:/app/src
      - ./configs:/app/configs
      - ./project.yaml:/app/project.yaml
      - ./data:/app/data
      - ./logs:/app/logs
      - ./eval:/app/eval
      
      - ../local.yml:/app/local.yml

      # - ./userconfigs:/app/userconfigs
      - ./.vscode:/app/.vscode
      # - /data/.vscode-server/extensions:/root/.vscode-server/extensions
      # - /data/.vscode-server/data:/root/.vscode-server/data
      - /data/.vscode-server/:/root/.vscode-server/
      - ./.vscode/machine/:/root/.vscode-server/data/Machine
      # - ./.vscode/machine/:/root/.vscode-server/data/Machine
      - /zih/gl4psgdata/:/datasets
      - /zih/pubSlpData/:/datasets2
      - /data/records:/opt/records
      - /data/records-domino:/opt/records-domino
      - /data/public-datasets/:/app/datasets
    command: -v
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              device_ids: [ '1' ]
              capabilities: [ gpu ]
