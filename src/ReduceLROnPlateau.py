from pathlib import Path
from pyPhases import classLogger
from pyPhasesML.Model import ModelConfig
from pyPhasesML.adapter.torch.Callback import Callback

@classLogger
class ReduceLROnPlateau(Callback):
    def __init__(self, config: ModelConfig, monitor='loss', patience=3, factor=0.1, min_lr=1e-6, priority=100) -> None:
        """
        Reduce learning rate when metric(s) have stopped improving.
       
        Args:
            config: Model configuration
            monitor: Metric(s) to monitor for improvement. Can be a string or list of strings (default: 'loss')
            patience: Number of epochs with no improvement after which learning rate will be reduced
            factor: Factor by which the learning rate will be reduced (new_lr = lr * factor)
            min_lr: Lower bound on the learning rate
            priority: Callback priority
        """
        super().__init__(config, priority)
        
        # Handle both single metric and list of metrics
        if isinstance(monitor, str):
            self.monitors = [monitor]
        elif isinstance(monitor, (list, tuple)):
            self.monitors = list(monitor)
        else:
            raise ValueError("monitor must be a string or list of strings")
        
        self.patience = patience
        self.factor = factor
        self.min_lr = min_lr
        
        # Track best metrics and wait counters for each monitored metric
        self.best_metrics = {}
        self.wait_counters = {}
        self.best_epochs = {}
        self.modes = {}
        
        # Initialize tracking for each metric
        for metric in self.monitors:
            self.best_metrics[metric] = None
            self.wait_counters[metric] = 0
            self.best_epochs[metric] = 0
            # Use 'min' mode for loss metrics, 'max' for others
            self.modes[metric] = 'min' if 'loss' in metric.lower() else 'max'
       
    def onValidationEnd(self, model, results, scorer):        
        # Check which monitored metrics are available
        available_metrics = [m for m in self.monitors if m in results]
        missing_metrics = [m for m in self.monitors if m not in results]
        
        if missing_metrics:
            self.logWarning(f"Monitor metrics {missing_metrics} not in results: {list(results.keys())}")
        
        if not available_metrics:
            self.logError(f"None of the monitor metrics {self.monitors} found in results")
            return
        
        # Process each available metric
        metrics_improved = []
        for metric in available_metrics:
            current = results[metric]
            
            # Initialize best_metric if not set
            if self.best_metrics[metric] is None:
                self.best_metrics[metric] = current
                self.best_epochs[metric] = model.epoch
                metrics_improved.append(True)
                continue
            
            # Check if improved based on mode
            improved = False
            if self.modes[metric] == 'min':
                improved = current < self.best_metrics[metric]
            else:
                improved = current > self.best_metrics[metric]
            
            if improved:
                self.best_metrics[metric] = current
                self.wait_counters[metric] = 0
                self.best_epochs[metric] = model.epoch
                metrics_improved.append(True)
            else:
                self.wait_counters[metric] += 1
                metrics_improved.append(False)
        
        # Check if we should reduce learning rate
        # Option 1: Reduce LR if ALL metrics haven't improved for patience epochs
        all_stagnant = all(self.wait_counters[m] >= self.patience for m in available_metrics)
        
        if all_stagnant:
            self.reduce_lr(model, available_metrics)
            # Reset all wait counters after reducing LR
            for metric in available_metrics:
                self.wait_counters[metric] = 0
               
    def reduce_lr(self, model, metrics_checked):
        """Reduce learning rate for all parameter groups in optimizer."""
        if not hasattr(model, 'optimizer') or model.optimizer is None:
            return
           
        for param_group in model.optimizer.param_groups:
            old_lr = param_group['lr']
            new_lr = max(old_lr * self.factor, self.min_lr)
            param_group['lr'] = new_lr
           
            self.log(f"Learning rate reduced from {old_lr:.6f} to {new_lr:.6f} after {self.patience} epochs without improvement")
            
            # Log details for each metric
            for metric in metrics_checked:
                best_val = self.best_metrics[metric]
                best_epoch = self.best_epochs[metric]
                wait_count = self.wait_counters[metric]
                self.log(f"  {metric}: best={best_val:.6f} at epoch {best_epoch}, stagnant for {wait_count} epochs")
               
    def getResumePath(self):
        return Path(self.getLogPath(), ".resumeLRReducerState.pt")
       
    def onCheckpoint(self, model):
        """Save state dict when checkpoint is created."""
        import torch
        state = {
            'best_metrics': self.best_metrics,
            'wait_counters': self.wait_counters,
            'best_epochs': self.best_epochs,
            'monitors': self.monitors,
            'modes': self.modes
        }
        torch.save(state, str(self.getResumePath()))
       
    def onRestore(self, model):
        """Restore state when checkpoint is loaded."""
        import torch
        import os
       
        resume_path = self.getResumePath()
        if os.path.exists(resume_path):
            state = torch.load(str(resume_path), weights_only=False)
            self.best_metrics = state.get('best_metrics', {})
            self.wait_counters = state.get('wait_counters', {})
            self.best_epochs = state.get('best_epochs', {})
            
            # Handle backward compatibility
            if 'monitors' in state:
                restored_monitors = state['monitors']
                if restored_monitors != self.monitors:
                    self.logWarning(f"Restored monitors {restored_monitors} differ from current {self.monitors}")
            
            if 'modes' in state:
                self.modes.update(state['modes'])
            
            self.log(f"Restored LR reducer state for metrics: {list(self.best_metrics.keys())}")
            for metric, best_val in self.best_metrics.items():
                wait = self.wait_counters.get(metric, 0)
                self.log(f"  {metric}: best={best_val}, wait={wait}")


# Alternative implementation with different reduction strategies
@classLogger 
class ReduceLROnPlateauAdvanced(Callback):
    def __init__(self, config: ModelConfig, monitor='loss', patience=3, factor=0.1, min_lr=1e-6, 
                 strategy='all_stagnant', priority=100) -> None:
        """
        Advanced reduce learning rate callback with multiple strategies.
       
        Args:
            config: Model configuration
            monitor: Metric(s) to monitor for improvement. Can be a string or list of strings
            patience: Number of epochs with no improvement after which learning rate will be reduced
            factor: Factor by which the learning rate will be reduced
            min_lr: Lower bound on the learning rate
            strategy: Strategy for multiple metrics:
                     'all_stagnant' - reduce when ALL metrics are stagnant
                     'any_stagnant' - reduce when ANY metric is stagnant
                     'majority_stagnant' - reduce when majority of metrics are stagnant
            priority: Callback priority
        """
        super().__init__(config, priority)
        
        if isinstance(monitor, str):
            self.monitors = [monitor]
        elif isinstance(monitor, (list, tuple)):
            self.monitors = list(monitor)
        else:
            raise ValueError("monitor must be a string or list of strings")
        
        self.patience = patience
        self.factor = factor
        self.min_lr = min_lr
        self.strategy = strategy
        
        if strategy not in ['all_stagnant', 'any_stagnant', 'majority_stagnant']:
            raise ValueError("strategy must be one of: 'all_stagnant', 'any_stagnant', 'majority_stagnant'")
        
        # Initialize tracking
        self.best_metrics = {}
        self.wait_counters = {}
        self.best_epochs = {}
        self.modes = {}
        
        for metric in self.monitors:
            self.best_metrics[metric] = None
            self.wait_counters[metric] = 0
            self.best_epochs[metric] = 0
            self.modes[metric] = 'min' if 'loss' in metric.lower() else 'max'
    
    def onValidationEnd(self, model, results, scorer):
        if not results:
            return
        
        available_metrics = [m for m in self.monitors if m in results]
        if not available_metrics:
            return
        
        # Update metrics
        stagnant_count = 0
        for metric in available_metrics:
            current = results[metric]
            
            if self.best_metrics[metric] is None:
                self.best_metrics[metric] = current
                self.best_epochs[metric] = model.epoch
                continue
            
            # Check improvement
            improved = False
            if self.modes[metric] == 'min':
                improved = current < self.best_metrics[metric]
            else:
                improved = current > self.best_metrics[metric]
            
            if improved:
                self.best_metrics[metric] = current
                self.wait_counters[metric] = 0
                self.best_epochs[metric] = model.epoch
            else:
                self.wait_counters[metric] += 1
                if self.wait_counters[metric] >= self.patience:
                    stagnant_count += 1
        
        # Apply strategy
        should_reduce = False
        total_metrics = len(available_metrics)
        
        if self.strategy == 'all_stagnant':
            should_reduce = stagnant_count == total_metrics
        elif self.strategy == 'any_stagnant':
            should_reduce = stagnant_count > 0
        elif self.strategy == 'majority_stagnant':
            should_reduce = stagnant_count > total_metrics // 2
        
        if should_reduce:
            self.reduce_lr(model, available_metrics)
            # Reset counters for stagnant metrics
            for metric in available_metrics:
                if self.wait_counters[metric] >= self.patience:
                    self.wait_counters[metric] = 0
    
    def reduce_lr(self, model, metrics_checked):
        """Reduce learning rate for all parameter groups in optimizer."""
        if not hasattr(model, 'optimizer') or model.optimizer is None:
            return
           
        for param_group in model.optimizer.param_groups:
            old_lr = param_group['lr']
            new_lr = max(old_lr * self.factor, self.min_lr)
            param_group['lr'] = new_lr
           
            self.log(f"Learning rate reduced from {old_lr:.6f} to {new_lr:.6f} using '{self.strategy}' strategy")
            
            for metric in metrics_checked:
                if self.wait_counters[metric] >= self.patience:
                    best_val = self.best_metrics[metric]
                    best_epoch = self.best_epochs[metric]
                    self.log(f"  {metric}: stagnant - best={best_val:.6f} at epoch {best_epoch}")
    
    def getResumePath(self):
        return Path(self.getLogPath(), ".resumeLRReducerAdvancedState.pt")
       
    def onCheckpoint(self, model):
        """Save state dict when checkpoint is created."""
        import torch
        state = {
            'best_metrics': self.best_metrics,
            'wait_counters': self.wait_counters,
            'best_epochs': self.best_epochs,
            'monitors': self.monitors,
            'modes': self.modes,
            'strategy': self.strategy
        }
        torch.save(state, str(self.getResumePath()))
       
    def onRestore(self, model):
        """Restore state when checkpoint is loaded."""
        import torch
        import os
       
        resume_path = self.getResumePath()
        if os.path.exists(resume_path):
            state = torch.load(str(resume_path), weights_only=False)
            self.best_metrics = state.get('best_metrics', {})
            self.wait_counters = state.get('wait_counters', {})
            self.best_epochs = state.get('best_epochs', {})
            
            if 'strategy' in state and state['strategy'] != self.strategy:
                self.logWarning(f"Restored strategy {state['strategy']} differs from current {self.strategy}")
            
            self.log(f"Restored advanced LR reducer state with strategy '{self.strategy}'")