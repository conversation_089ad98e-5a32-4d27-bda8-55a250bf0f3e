#!/usr/bin/env python3
"""
Test script to verify interpretability analysis components work correctly
"""

import sys
import os
sys.path.append('/app')
import numpy as np

def test_imports():
    """Test that all required imports work"""
    print("Testing imports...")
    
    try:
        # Core imports
        import torch
        import numpy as np
        import matplotlib.pyplot as plt
        print("✓ Core imports successful")
        
        # Analysis imports
        from src.analysis.captum_analyzer import CaptumModelAnalyzer
        print("✓ CaptumModelAnalyzer import successful")
        
        from src.analysis.sleep_visualizer import SleepVisualizationSuite
        print("✓ SleepVisualizationSuite import successful")
        
        from src.analysis.model_inspector import ModelAnalyzer
        print("✓ ModelAnalyzer import successful")
        
        # Optional imports
        try:
            import scipy.signal as signal
            print("✓ SciPy signal import successful")
        except ImportError:
            print("⚠ SciPy not available - some analysis features may be limited")
        
        try:
            import seaborn as sns
            print("✓ Seaborn import successful")
        except ImportError:
            print("⚠ Seaborn not available - some visualizations may be limited")
            
        return True
        
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False

def test_model_loading():
    """Test model loading functionality"""
    print("\nTesting model loading...")
    
    try:
        import phases
        import torch
        from pyPhasesML import ModelManager
        
        # Change to app directory
        os.chdir('/app')
        
        # Load project
        project = phases.loadProject('project.yaml', 'local.yml,configs/full.yml,configs/apnea.yml,configs/dsds.yml,configs/dsds-multi.yml')
        print("✓ Project loaded successfully")
        
        # Test data loader
        data_loader = project.getData("dataset-validation", np.ndarray)
        print("✓ Data loader created successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Model loading failed: {e}")
        return False

def test_analyzer_initialization():
    """Test analyzer initialization"""
    print("\nTesting analyzer initialization...")
    
    try:
        import torch
        from src.analysis.captum_analyzer import CaptumModelAnalyzer
        from src.analysis.sleep_visualizer import SleepVisualizationSuite
        from src.analysis.model_inspector import ModelAnalyzer
        
        # Create dummy model for testing
        class DummyModel(torch.nn.Module):
            def __init__(self):
                super().__init__()
                self.multiClassNums = [5, 1, 1, 1]  # Sleep, Arousal, Apnea, LM
                
            def forward(self, x):
                batch_size = x.shape[0]
                return [
                    torch.randn(batch_size, 1200, 5),  # Sleep stages
                    torch.randn(batch_size, 36000, 1),  # Arousal
                    torch.randn(batch_size, 36000, 1),  # Apnea
                    torch.randn(batch_size, 36000, 1),  # LM
                ]
        
        dummy_model = DummyModel()
        device = 'cpu'
        
        # Test CaptumModelAnalyzer
        captum_analyzer = CaptumModelAnalyzer(
            model=dummy_model,
            device=device,
            task_names=['Sleep', 'Arousal', 'Apnea', 'LM']
        )
        print("✓ CaptumModelAnalyzer initialized successfully")
        
        # Test SleepVisualizationSuite
        sleep_viz = SleepVisualizationSuite(
            model=dummy_model,
            device=device,
            sampling_rate=100
        )
        print("✓ SleepVisualizationSuite initialized successfully")
        
        # Test ModelAnalyzer
        model_analyzer = ModelAnalyzer(dummy_model, (2304000, 10))
        print("✓ ModelAnalyzer initialized successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Analyzer initialization failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=== PPGNet Interpretability Analysis Test ===\n")
    
    tests = [
        test_imports,
        test_model_loading,
        test_analyzer_initialization
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            results.append(False)
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {sum(results)}/{len(results)}")
    
    if all(results):
        print("🎉 All tests passed! The interpretability notebook should work correctly.")
    else:
        print("⚠ Some tests failed. Check the errors above and fix before running the notebook.")
    
    return all(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
