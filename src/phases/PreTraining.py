from pathlib import Path
from pyPhases import Phase

from pyPhasesML import Model, ModelManager
from pyPhasesML.exporter.ModelExporter import ModelExporter

class PreTraining(Phase):
    def main(self):

        pretrainedPath = self.getConfig("pretrainedModel")
        
        if not Path(pretrainedPath).exists():            
            with self.project:
                pretrainingConfig = self.getConfig("pretraining")
                self.project.config["dataversion"] = {}
                self.project.config.update(pretrainingConfig)
                self.project.trigger("configChanged", None)
                preModelState = self.project.getData("modelState", Model)

        else:
            import torch
            
            deviceName = "cuda" if torch.cuda.is_available() else "cpu"
            preModelState = torch.load(pretrainedPath, map_location=torch.device(deviceName))
        
        # set pretrain        
        model = ModelManager.getModel(True)
        model.build()
        model.loadState(preModelState)
        self.project.getData("modelState", Model)
