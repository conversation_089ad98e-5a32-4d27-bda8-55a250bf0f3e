from pyPhases import Phase

from SleePyPhases import SignalPreprocessing as SP, PreManipulation as PM, FeatureExtraction as FE, DataManipulation as DM
from SleepHarmonizer.SignalPreprocessing import SignalPreprocessing as SPHarm

from src.SignalPreprocessing import SignalPreprocessing
from src.DataManipulation import DataManipulation

class Init(Phase):
    def prepareConfig(self):
        SP.setClass(SignalPreprocessing)
        DM.setClass(DataManipulation)
        # SPHarm.setClass(SignalPreprocessing)
    
    # Code used to generate predefined list: run `phases run Init`
    # def main(self):
    #     import pandas as pd
    #     df = self.getData("metadata", pd.DataFrame)

    #     testRecordIds = self.getConfig("dataversion.recordIds") # only test-set specified
    #     allRecords = df["recordId"].tolist()
    #     trainValRecords = [r for r in allRecords if r not in testRecordIds]
        
    #     import random

    #     seed_value = self.getConfig("dataversion.trainval_seed")  # You can choose any integer value
    #     random.seed(seed_value)
    #     random.shuffle(trainValRecords)
    #     print("\n    - ".join(trainValRecords)) # add to dataversion.recordIds in configs/predefined.yml
