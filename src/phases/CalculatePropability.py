from pyPhases import Phase
import numpy as np

class CalculatePropability(Phase):
    def main(self):
        memmapOptions = {
            "dtype": self.getConfig("preprocessing.dtype", "float32"),
        }
        preprocessed = self.project.getData("data-features", np.memmap, options=memmapOptions)

        numClasses = 4
        sleepStages = [0 for i in range(numClasses)]
        for recordY in preprocessed:
            sleepStages[0] += (recordY == 0).sum()
            sleepStages[1] += (recordY == 1).sum()
            sleepStages[2] += (recordY == 2).sum() + (recordY == 3).sum()
            sleepStages[3] += (recordY == 4).sum()
            
        count = sum(sleepStages)
        propabilty = [c / count for c in sleepStages]
        weights = np.array([1/(c) for c in sleepStages])
        weights = weights * (4 / weights.sum())
        self.logSuccess(f"Class Count: {sleepStages}")
        self.logSuccess(f"Class Propability: {propabilty}")