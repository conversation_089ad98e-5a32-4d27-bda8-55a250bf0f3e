from pathlib import Path
from pyPhases import Phase

from pyPhasesML import Model, ModelManager
from pyPhasesML.exporter.ModelExporter import ModelExporter

from matplotlib import pyplot as plt
import numpy as np
import torch
from src.analysis.captum_analyzer import CaptumModelAnalyzer
import seaborn as sns
from src.analysis.sleep_visualizer import SleepVisualizationSuite
from src.analysis.signal_analyzer import PSGSignalAnalyzer

class ModelAnalysis(Phase):

    def analyze_model(self, model_path, data_loader, device):
        # Load model
        pyPhaseModelmodel = ModelManager.getModel()
        modelState = torch.load(model_path, map_location=device, weights_only=True)
        pyPhaseModelmodel.loadState(modelState)

        # def debug_dataloader(dataloader):
        #     for i in range(2):
        #         yield dataloader[i]
        
        # data_loader = debug_dataloader(data_loader)

        model = pyPhaseModelmodel.model
        model.eval()

        print("="*80)
        print("PSG SLEEP MODEL INTERPRETABILITY ANALYSIS")
        print("="*80)
        
        # Run comprehensive PSG-based analysis
        self.run_comprehensive_psg_analysis(model, data_loader, device)

    def run_comprehensive_psg_analysis(self, model, data_loader, device):
        """Run comprehensive PSG-focused analysis"""
        
        print("\nInitializing PSG Captum analyzer...")
        captum_analyzer = CaptumModelAnalyzer(model, device, sampling_rate=self.getConfig("preprocessing.targetFrequency"))
        

        batch_x, batch_y = data_loader[14]
        sample_input = batch_x.to(device)
        sample_labels = batch_y

        
        print(f"Analyzing PSG recording:")
        print(f"  Input shape: {sample_input.shape}")
        print(f"  Duration: {sample_input.shape[-1] / 100 / 3600:.1f} hours")
        print(f"  PSG Channels: {sample_input.shape[1]}")
        
        # Show PSG channel information
        psg_channels = [
            'EEG',
            'EOG', 'EMG Chin', 'EMG Leg', 'Position', 'Snore',
            'Chest Effort', 'Abdominal Effort', 'SpO2', 'Airflow'
        ]
        
        n_channels = min(sample_input.shape[1], len(psg_channels))
        print(f"\n  Available PSG signals:")
        for i in range(n_channels):
            channel_name = psg_channels[i] if i < len(psg_channels) else f"Channel_{i}"
            print(f"    {i:2d}: {channel_name}")
        
        # Run the comprehensive analysis
        print("\n" + "="*60)
        print("STARTING COMPREHENSIVE PSG ANALYSIS")
        print("="*60)
        
        try:
            all_attributions = captum_analyzer.comprehensive_analysis(sample_input, sample_labels)
            
            # # Additional PSG-specific insights
            # self.generate_psg_clinical_insights(model, sample_input, sample_labels, device)
            
            # # PSG model architecture analysis
            # self.analyze_psg_model_architecture(model)
            
            # # PSG signal quality analysis
            # self.analyze_psg_signal_quality(sample_input, psg_channels)

            # Advanced visualizations
            print("\n" + "="*60)
            print("CREATING ADVANCED VISUALIZATIONS")
            print("="*60)

            # Initialize visualization suites
            sleep_visualizer = SleepVisualizationSuite(model, device)
            signal_analyzer = PSGSignalAnalyzer()

            # Create comprehensive visualizations with error handling
            visualizations = [
                # ("interactive hypnogram", lambda: sleep_visualizer.create_interactive_hypnogram(sample_input, sample_labels)),
                # ("spectral analysis", lambda: sleep_visualizer.create_spectral_analysis(sample_input, sample_labels)),
                ("correlation analysis", lambda: sleep_visualizer.create_correlation_analysis(sample_input, sample_labels)),
                ("model uncertainty analysis", lambda: sleep_visualizer.create_model_uncertainty_analysis(sample_input)),
                # ("clinical summary dashboard", lambda: sleep_visualizer.create_clinical_summary_dashboard(sample_input, sample_labels)),
                # ("signal quality analysis", lambda: signal_analyzer.analyze_signal_quality(sample_input, sample_labels)),
                # ("frequency analysis", lambda: signal_analyzer.create_frequency_analysis(sample_input, sample_labels)),
                # ("time domain analysis", lambda: signal_analyzer.create_time_domain_analysis(sample_input, sample_labels))
            ]

            successful_visualizations = []
            failed_visualizations = []

            for viz_name, viz_func in visualizations:
                try:
                    print(f"Creating {viz_name}...")
                    viz_func()
                    successful_visualizations.append(viz_name)
                    print(f"✅ {viz_name} completed successfully")
                except Exception as e:
                    print(f"❌ Error creating {viz_name}: {str(e)}")
                    failed_visualizations.append((viz_name, str(e)))
                    import traceback
                    print(f"   Traceback: {traceback.format_exc()}")

            # Summary of visualization results
            print(f"\n📊 Visualization Summary:")
            print(f"   ✅ Successful: {len(successful_visualizations)}")
            print(f"   ❌ Failed: {len(failed_visualizations)}")

            if successful_visualizations:
                print(f"   Completed visualizations:")
                for viz in successful_visualizations:
                    print(f"     - {viz}")

            if failed_visualizations:
                print(f"   Failed visualizations:")
                for viz, error in failed_visualizations:
                    print(f"     - {viz}: {error}")
                print(f"   Note: Check dependencies and data format if visualizations failed")

            print("\n" + "="*60)
            print("PSG ANALYSIS COMPLETE")
            print("="*60)
            print(f"Results saved to:")
            print(f"  - captum_analysis/ (Attribution analysis)")
            print(f"  - sleep_visualizations/ (Sleep analysis)")
            print(f"  - signal_analysis/ (Signal quality)")
            print("Check the generated plots and reports for detailed PSG insights.")
            
        except Exception as e:
            print(f"ERROR during PSG analysis: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def generate_psg_clinical_insights(self, model, sample_input, sample_labels, device):
        """Generate clinical insights about PSG model behavior"""
        
        print("\n" + "="*50)
        print("PSG CLINICAL INSIGHTS")
        print("="*50)
        
        model.eval()
        with torch.no_grad():
            outputs = model(sample_input)
        
        # Analyze sleep architecture from PSG
        if len(outputs) > 0:
            sleep_output = outputs[0]  # Sleep staging
            sleep_probs = torch.softmax(sleep_output, dim=-1)
            predicted_stages = torch.argmax(sleep_probs, dim=-1)
            
            print("\nSleep Architecture from PSG Analysis:")
            stage_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
            total_epochs = predicted_stages.shape[1]
            total_sleep_time = 0
            
            stage_durations = {}
            for i, stage in enumerate(stage_names):
                count = (predicted_stages[0] == i).sum().item()
                percentage = count / total_epochs * 100
                duration_min = count * 0.5  # 30-second epochs
                stage_durations[stage] = duration_min
                
                if stage != 'Wake':
                    total_sleep_time += duration_min
                
                print(f"  {stage}: {count} epochs ({percentage:.1f}%, {duration_min:.0f} min)")
            
            # Sleep efficiency and architecture metrics
            sleep_efficiency = (total_sleep_time / (total_epochs * 0.5)) * 100
            print(f"\n  Sleep Efficiency: {sleep_efficiency:.1f}%")
            
            # Sleep stage percentages (of total sleep time)
            if total_sleep_time > 0:
                print(f"  Sleep Stage Distribution (% of sleep time):")
                for stage in ['N1', 'N2', 'N3', 'REM']:
                    sleep_percentage = (stage_durations[stage] / total_sleep_time) * 100
                    print(f"    {stage}: {sleep_percentage:.1f}%")
                    
                    # Clinical norms
                    if stage == 'N3' and sleep_percentage < 15:
                        print(f"      ⚠️  Low deep sleep (normal: 15-20%)")
                    elif stage == 'REM' and sleep_percentage < 20:
                        print(f"      ⚠️  Low REM sleep (normal: 20-25%)")
            
            # REM latency analysis
            rem_epochs = torch.where(predicted_stages[0] == 4)[0]
            if len(rem_epochs) > 0:
                rem_latency_min = rem_epochs[0].item() * 0.5
                print(f"  REM Latency: {rem_latency_min:.0f} minutes")
                if rem_latency_min < 60:
                    print(f"    ⚠️  Short REM latency (may indicate sleep disorder)")
        
        # Analyze respiratory events from PSG
        event_names = ['Arousal', 'Apnea', 'Limb Movement']
        for i, event_name in enumerate(event_names):
            if i + 1 < len(outputs):
                event_output = outputs[i + 1]
                
                if event_output.shape[-1] == 1:
                    # Binary events
                    event_probs = torch.sigmoid(event_output).squeeze(-1)
                    events = (event_probs > 0.5).sum().item()
                    total_seconds = event_output.shape[1]
                    event_rate = events / (total_seconds / 3600)  # Events per hour
                    
                    print(f"\n{event_name} Analysis from PSG:")
                    print(f"  Total events: {events}")
                    print(f"  Event rate: {event_rate:.1f} events/hour")
                    print(f"  Event percentage: {events/total_seconds*100:.2f}% of recording")
                    
                    # Clinical significance
                    if event_name == 'Apnea':
                        if event_rate >= 30:
                            print(f"  🔴 Severe sleep apnea (AHI ≥30)")
                        elif event_rate >= 15:
                            print(f"  🟡 Moderate sleep apnea (AHI 15-29)")
                        elif event_rate >= 5:
                            print(f"  🟠 Mild sleep apnea (AHI 5-14)")
                        else:
                            print(f"  ✅ Normal breathing (AHI <5)")
                    
                    elif event_name == 'Arousal':
                        if event_rate > 15:
                            print(f"  ⚠️  High arousal index (>15/hour indicates fragmented sleep)")
                        else:
                            print(f"  ✅ Normal arousal index")
                    
                    elif event_name == 'Limb Movement':
                        if event_rate > 15:
                            print(f"  ⚠️  High limb movement index (may indicate PLMS)")
    
    def analyze_psg_model_architecture(self, model):
        """Analyze model architecture for PSG analysis"""
        
        print("\n" + "="*50)
        print("PSG MODEL ARCHITECTURE ANALYSIS")
        print("="*50)
        
        # Count parameters
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"PSG Model Parameters:")
        print(f"  Total: {total_params:,}")
        print(f"  Trainable: {trainable_params:,}")
        
        # Analyze PSG-specific architecture
        print(f"\nPSG Processing Architecture:")
        
        # Feature extraction for multi-channel PSG
        if hasattr(model, 'fe_layers'):
            fe_params = sum(p.numel() for p in model.fe_layers.parameters())
            print(f"  Multi-channel Feature Extraction: {fe_params:,} parameters")
            print(f"    Purpose: Extract features from 16 PSG channels simultaneously")
            print(f"    Handles: EEG, EOG, EMG, respiratory, and physiological signals")
        
        # Sleep staging network
        if hasattr(model, 'sleep_tcn1'):
            sleep_params = sum(p.numel() for p in model.sleep_tcn1.parameters())
            if hasattr(model, 'sleep_tcn2'):
                sleep_params += sum(p.numel() for p in model.sleep_tcn2.parameters())
            print(f"  Sleep Staging TCN: {sleep_params:,} parameters")
            print(f"    Purpose: Long-term temporal analysis for 30-second epochs")
            print(f"    Integrates: EEG patterns, eye movements, muscle tone")
        
        # Event detection network
        if hasattr(model, 'micro_tcn1'):
            micro_params = sum(p.numel() for p in model.micro_tcn1.parameters())
            if hasattr(model, 'micro_tcn2'):
                micro_params += sum(p.numel() for p in model.micro_tcn2.parameters())
            print(f"  Event Detection TCN: {micro_params:,} parameters")
            print(f"    Purpose: Short-term event detection at 1-second resolution")
            print(f"    Detects: Apneas, arousals, limb movements")
        
        # Multi-task outputs
        if hasattr(model, 'fcs'):
            fc_params = sum(sum(p.numel() for p in fc.parameters()) for fc in model.fcs)
            print(f"  Classification Heads: {fc_params:,} parameters")
            print(f"    Sleep stages: 5 classes (Wake, N1, N2, N3, REM)")
            print(f"    Events: Binary/multi-class detection")
        
        # PSG temporal analysis capability
        print(f"\nPSG Temporal Analysis:")
        
        # Sleep staging temporal context
        if hasattr(model, 'sleep_tcn1'):
            sleep_rf = self.estimate_tcn_receptive_field([1,2,4,8,16,32], kernel_size=7)
            sleep_context_min = (sleep_rf / 100) / 60
            print(f"  Sleep staging context: ~{sleep_rf} samples ({sleep_context_min:.1f} minutes)")
            print(f"    Analyzes: Extended EEG/EOG patterns for accurate staging")
        
        # Event detection temporal context
        if hasattr(model, 'micro_tcn1'):
            event_rf = self.estimate_tcn_receptive_field([1,2,4,8], kernel_size=7)
            event_context_sec = event_rf / 100
            print(f"  Event detection context: ~{event_rf} samples ({event_context_sec:.1f} seconds)")
            print(f"    Analyzes: Rapid changes in respiratory/EMG signals")
        
        # PSG multi-modal integration
        print(f"\nPSG Multi-Modal Integration:")
        print(f"  EEG channels (6): Brain activity patterns for sleep staging")
        print(f"  EOG channels (2): Eye movement detection for REM/Wake")
        print(f"  EMG channels (2): Muscle tone for staging and movement detection")
        print(f"  Respiratory (4): Airflow, effort, snoring for apnea detection")
        print(f"  Physiological (2): SpO2, position for comprehensive analysis")
    
    def analyze_psg_signal_quality(self, sample_input, psg_channels):
        """Analyze PSG signal quality and characteristics"""
        
        print("\n" + "="*50)
        print("PSG SIGNAL QUALITY ANALYSIS")
        print("="*50)
        
        n_channels = min(sample_input.shape[1], len(psg_channels))
        
        print(f"Signal Quality Assessment:")
        
        for i in range(n_channels):
            channel_name = psg_channels[i] if i < len(psg_channels) else f"Channel_{i}"
            signal = sample_input[0, i].cpu().numpy()
            
            # Basic signal statistics
            signal_mean = np.mean(signal)
            signal_std = np.std(signal)
            signal_range = np.max(signal) - np.min(signal)
            
            # Signal quality indicators
            zero_percentage = np.mean(signal == 0) * 100
            saturation_percentage = np.mean(np.abs(signal) > np.percentile(np.abs(signal), 99)) * 100
            
            print(f"\n  {channel_name}:")
            print(f"    Mean: {signal_mean:.3f}, Std: {signal_std:.3f}")
            print(f"    Range: {signal_range:.3f}")
            print(f"    Zero values: {zero_percentage:.1f}%")
            print(f"    Potential artifacts: {saturation_percentage:.1f}%")
            
            # Channel-specific quality assessment
            if 'EEG' in channel_name:
                # EEG should have reasonable amplitude and variability
                if signal_std < 10:
                    print(f"    ⚠️  Low EEG variability (possible electrode issue)")
                elif signal_std > 200:
                    print(f"    ⚠️  High EEG noise (possible artifact)")
                else:
                    print(f"    ✅ EEG signal quality appears good")
            
            elif 'EOG' in channel_name:
                # EOG should show eye movement patterns
                if signal_std < 5:
                    print(f"    ⚠️  Low EOG variability (possible electrode issue)")
                elif zero_percentage > 10:
                    print(f"    ⚠️  Too many zero values in EOG")
                else:
                    print(f"    ✅ EOG signal quality appears good")
            
            elif 'EMG' in channel_name:
                # EMG should have muscle activity patterns
                if signal_std < 2:
                    print(f"    ⚠️  Very low EMG activity (check electrode contact)")
                elif signal_range > 1000:
                    print(f"    ⚠️  High EMG artifacts detected")
                else:
                    print(f"    ✅ EMG signal quality appears good")
            
            elif any(resp in channel_name for resp in ['Flow', 'Chest', 'Abdominal']):
                # Respiratory signals should show breathing patterns
                if signal_std < 1:
                    print(f"    ⚠️  Low respiratory signal variability")
                elif zero_percentage > 5:
                    print(f"    ⚠️  Respiratory signal dropouts detected")
                else:
                    print(f"    ✅ Respiratory signal quality appears good")
            
            elif 'SpO2' in channel_name:
                # SpO2 should be in physiological range (typically 85-100%)
                if np.min(signal) < 70 or np.max(signal) > 105:
                    print(f"    ⚠️  SpO2 values outside physiological range")
                elif signal_std < 1:
                    print(f"    ⚠️  SpO2 signal too stable (possible sensor issue)")
                else:
                    print(f"    ✅ SpO2 signal quality appears good")
        
        # Overall PSG recording quality
        print(f"\nOverall PSG Recording Assessment:")
        
        # Check for synchronized signals
        total_duration_hours = sample_input.shape[-1] / 100 / 3600
        print(f"  Recording duration: {total_duration_hours:.1f} hours")
        
        if total_duration_hours < 6:
            print(f"  ⚠️  Short recording duration (minimum 6 hours recommended)")
        elif total_duration_hours > 12:
            print(f"  ⚠️  Very long recording (check for proper sleep period)")
        else:
            print(f"  ✅ Appropriate recording duration")
        
        # Signal synchronization check
        non_zero_channels = sum(1 for i in range(n_channels) 
                               if np.std(sample_input[0, i].cpu().numpy()) > 0.1)
        
        print(f"  Active channels: {non_zero_channels}/{n_channels}")
        
        if non_zero_channels < 10:
            print(f"  ⚠️  Many channels appear inactive")
        else:
            print(f"  ✅ Most PSG channels are active")
    
    def estimate_tcn_receptive_field(self, dilations, kernel_size=7):
        """Estimate TCN receptive field"""
        rf = 1
        for dilation in dilations:
            rf += (kernel_size - 1) * dilation
        return rf
    
    def main(self):
        """Main PSG analysis entry point"""
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {device}")
        
        # Configuration
        configPath = "logs/TSM-3edb8857-f98e6a85-4ff76fed-3edb8857-SleepPPGNet-eb733d78-7f1c42f1-8a35a439-2128c9d5-0/model.config"
        model_path = "logs/TSM-3edb8857-f98e6a85-4ff76fed-3edb8857-SleepPPGNet-eb733d78-7f1c42f1-8a35a439-2128c9d5-0/checkpointModel_64_0.734_0.807_0.561_0.400_0.387_543.000_0.644_0.623_0.831_0.727_0.715_289.000.pkl"
        
        # Load configuration and data
        self.setConfig("trainingParameter.batchSize", 1)
        data_loader = self.getData("dataset-validation", np.ndarray)
        self.project.loadAndApplyConfig(configPath)
        
        # Run analysis
        self.analyze_model(model_path, data_loader, device)
        
