from pyPhasesRecordloader import RecordSignal, Signal
from SleePyPhases import SignalPreprocessing as pyPhaseSignalPreprocessing
import numpy as np
from scipy.signal import resample, firwin, sosfiltfilt, cheby2, fftconvolve, butter, filtfilt, iirnotch, resample_poly
from scipy.interpolate import interp1d


class SignalPreprocessing(pyPhaseSignalPreprocessing):

    def resample(self, signal: Signal, recordSignal: RecordSignal, targetFrequency=None):
        targetFrequency = targetFrequency or recordSignal.targetFrequency

        signalLength = int(len(signal.signal) * targetFrequency / signal.frequency)
        signal.signal = resample(signal.signal, signalLength)
        signal.frequency = targetFrequency


    def fir(self, signal: Signal, recordSignal: RecordSignal, nFir, cutoff, pass_zero=False):

        fs = signal.frequency
        nyq = fs/2

        band = [cutoff[0]/nyq, cutoff[1]/nyq] if isinstance(cutoff, list) else cutoff/nyq
        b = firwin(nFir, band, pass_zero=pass_zero)
        signal.signal = filtfilt(b, [1], signal.signal)


    def filter(self, signal: Signal, recordSignal: RecordSignal):
        
        # 1. Apply low-pass Chebyshev Type II filter
        # 8th order, 8Hz cutoff, 40dB attenuation
        nyquist = signal.frequency / 2
        stopband_attenuation = 40  # dB
        cutoff_freq = 8  # Hz
        order = 8
        
        # Design filter
        sos = cheby2(order, stopband_attenuation, cutoff_freq/nyquist, 'low', output='sos')
        
        # Apply zero-phase filtering
        signal.signal = sosfiltfilt(sos, signal.signal)
        
    def clip(self, signal: Signal, recordSignal: RecordSignal, factorSTD=3):
        mean_val = np.mean(signal.signal)
        std_val = np.std(signal.signal)
        signal.signal = np.clip(signal.signal, 
                                mean_val - factorSTD * std_val, 
                                mean_val + factorSTD * std_val)
        
    
    def fftConvolution(self, signal: Signal, recordSignal: RecordSignal, kernselSeconds):
        
        kernel_size = int(kernselSeconds * signal.frequency) + 1

        # Compute and remove moving average with FFT convolution
        resultShape = signal.signal.shape
        center = np.zeros(resultShape)

        center = fftconvolve(signal.signal, np.ones(shape=(kernel_size,)) / kernel_size, mode="same")

        signal.signal = signal.signal - center

        # Compute and remove the rms with FFT convolution of squared signal
        scale = np.ones(resultShape)

        temp = fftconvolve(np.square(signal.signal), np.ones(shape=(kernel_size,)) / kernel_size, mode="same")

        # Deal with negative values (mathematically, it should never be negative, but fft artifacts can cause this)
        temp[temp < 0] = 0.0

        # Deal with invalid values
        invalidIndices = np.isnan(temp) | np.isinf(temp)
        temp[invalidIndices] = 0.0
        maxTemp = np.max(temp)
        temp[invalidIndices] = maxTemp

        # Finish rms calculation
        scale = np.sqrt(temp)

        # To correct records that have a zero amplitude signal
        scale[(scale == 0) | np.isinf(scale) | np.isnan(scale)] = 1.0
        signal.signal = signal.signal / scale

    def fftConvolution18m(self, signal: Signal, recordSignal: RecordSignal):
        self.fftConvolution(signal, recordSignal, 18 * 60)
        
    
    def resampleSimple(self, signal: Signal, recordSignal: RecordSignal, targetFrequency=None):
        targetFrequency = targetFrequency or recordSignal.targetFrequency
        factor = signal.frequency / targetFrequency
        if factor >= 1:
            signal.signal = signal.signal[:: int(factor)]
        else:
            signal.signal = np.repeat(signal.signal, int(1 / factor), axis=0)
        signal.frequency = targetFrequency

    def standardize(self, signal: Signal, recordSignal: RecordSignal, factorSTD=3):
        
        mean_val = np.mean(signal.signal)
        std_val = np.std(signal.signal)
        signal.signal = (signal.signal - mean_val) / std_val
        

    # test


    def _zerophase_filter(self, b, a, x):
        """Applies a filter forward and backward using filtfilt."""
        return filtfilt(b, a, x)

    def highpass(self, signal_obj: Signal, record_signal_obj: RecordSignal, cutoff: float, order: int):
        # Nyquist frequency for the current signal
        nyq = 0.5 * signal_obj.frequency
        if cutoff >= nyq:
            print(f"Warning: Highpass cutoff ({cutoff} Hz) is at or above Nyquist ({nyq} Hz). Skipping filter for signal at {signal_obj.frequency} Hz.")
            return
        
        # Normalize cutoff by Nyquist frequency
        normal_cutoff = cutoff / nyq
        b, a = butter(order, normal_cutoff, btype='high', analog=False)
        signal_obj.signal = self._zerophase_filter(b, a, signal_obj.signal)

    def lowpass_antialias(self, signal_obj: Signal, record_signal_obj: RecordSignal, cutoff: float, order: int):
        nyq = 0.5 * signal_obj.frequency
        if cutoff >= nyq:
            # This should not happen if cutoff is chosen correctly (e.g. target_freq * 0.45)
            print(f"Warning: Lowpass anti-alias cutoff ({cutoff} Hz) is at or above Nyquist ({nyq} Hz). Adjusting cutoff.")
            cutoff = nyq * 0.98 # Adjust to be slightly below Nyquist
        
        normal_cutoff = cutoff / nyq
        b, a = butter(order, normal_cutoff, btype='low', analog=False)
        signal_obj.signal = self._zerophase_filter(b, a, signal_obj.signal)
        
    def lowpass_smooth(self, signal_obj: Signal, record_signal_obj: RecordSignal, cutoff: float, order: int):
        # This is typically applied *after* upsampling, so signal_obj.frequency is the target_freq
        nyq = 0.5 * signal_obj.frequency
        if cutoff >= nyq:
            print(f"Warning: Lowpass smooth cutoff ({cutoff} Hz) is at or above Nyquist ({nyq} Hz) for signal at {signal_obj.frequency} Hz. Skipping filter.")
            # Or adjust: cutoff = nyq * 0.98
            return

        normal_cutoff = cutoff / nyq
        b, a = butter(order, normal_cutoff, btype='low', analog=False)
        signal_obj.signal = self._zerophase_filter(b, a, signal_obj.signal)

    def notch(self, signal_obj: Signal, record_signal_obj: RecordSignal, freq: float, quality_factor: float):
        if freq <= 0 or freq >= signal_obj.frequency / 2:
            print(f"Warning: Notch frequency {freq} Hz is out of valid range for signal at {signal_obj.frequency} Hz. Skipping notch.")
            return
        b, a = iirnotch(freq, quality_factor, fs=signal_obj.frequency)
        signal_obj.signal = self._zerophase_filter(b, a, signal_obj.signal)

    def resample_poly(self, signal_obj: Signal, record_signal_obj: RecordSignal, target_freq: float):
        if signal_obj.frequency == target_freq:
            return

        current_len = len(signal_obj.signal)
        # Ensure integer up/down factors for resample_poly if possible, or it will find GCD
        # For simplicity, we directly use target_freq. Scipy's resample_poly handles this.
        # It's more robust to calculate num based on duration.
        duration = current_len / signal_obj.frequency
        num_target_samples = int(round(duration * target_freq))

        if num_target_samples == 0 and current_len > 0 : # Prevent empty array if duration is too short
            print(f"Warning: Target samples for resampling is 0. Original len: {current_len}, fs: {signal_obj.frequency}, target_fs: {target_freq}. Keeping original.")
            if current_len > 0: # If original signal was not empty, keep at least one sample
                 signal_obj.signal = np.array([signal_obj.signal[0]]*1) if signal_obj.frequency > target_freq else signal_obj.signal # crude
            else: # if original signal was empty
                 signal_obj.signal = np.array([])
            return


        # For downsampling, anti-aliasing should have been done *before* this step.
        # resample_poly includes its own polyphase anti-aliasing filter.
        resampled_signal = resample_poly(signal_obj.signal, up=int(target_freq), down=int(signal_obj.frequency))
        # Adjust length due to potential rounding in up/down integer conversion if not perfect ratio
        # A more robust way is to calculate the exact number of samples expected
        expected_samples = int(current_len * target_freq / signal_obj.frequency)
        
        # If resample_poly produces a slightly different length due to its rational approximation method:
        if len(resampled_signal) > expected_samples and expected_samples > 0:
            resampled_signal = resampled_signal[:expected_samples]
        elif len(resampled_signal) < expected_samples and len(resampled_signal)>0: # Pad if shorter (rare with resample_poly)
            padding = np.zeros(expected_samples - len(resampled_signal))
            resampled_signal = np.concatenate((resampled_signal, padding))


        signal_obj.signal = resampled_signal
        signal_obj.frequency = target_freq
        
    def resample_interpolate(self, signal_obj: Signal, record_signal_obj: RecordSignal, target_freq: float, kind='linear'):
        """Primarily for upsampling very slow signals like SpO2."""
        if signal_obj.frequency == target_freq:
            return

        current_time_vector = np.arange(len(signal_obj.signal)) / signal_obj.frequency
        target_num_samples = int(len(signal_obj.signal) * target_freq / signal_obj.frequency)
        if target_num_samples == 0 and len(signal_obj.signal) > 0: # Avoid empty array
            print(f"Warning: Target samples for interpolation is 0. Keeping original first sample or empty.")
            signal_obj.signal = np.array([signal_obj.signal[0]]) if len(signal_obj.signal) > 0 else np.array([])
            signal_obj.frequency = target_freq # Still update frequency
            return
        elif target_num_samples == 0 and len(signal_obj.signal) == 0:
            signal_obj.signal = np.array([])
            signal_obj.frequency = target_freq
            return

        target_time_vector = np.arange(target_num_samples) / target_freq
        
        if len(current_time_vector) < 2: # Need at least 2 points for interpolation
            if len(current_time_vector) == 1:
                signal_obj.signal = np.full(target_num_samples, signal_obj.signal[0]) # Repeat the single value
            else: # Empty signal
                signal_obj.signal = np.array([])
            signal_obj.frequency = target_freq
            return

        interp_func = interp1d(current_time_vector, signal_obj.signal, kind=kind, bounds_error=False, fill_value=(signal_obj.signal[0], signal_obj.signal[-1]))
        signal_obj.signal = interp_func(target_time_vector)
        signal_obj.frequency = target_freq

    def clip_fixed(self, signal_obj: Signal, record_signal_obj: RecordSignal, min_val: float, max_val: float):
        signal_obj.signal = np.clip(signal_obj.signal, min_val, max_val)

    def standardize(self, signal_obj: Signal, record_signal_obj: RecordSignal):
        """Global Z-score normalization for the entire signal."""
        # Check for near-zero std to avoid division by zero or large values
        s_std = np.std(signal_obj.signal)
        if s_std < 1e-9: # If std is very small (e.g. flat signal)
            if np.mean(signal_obj.signal) < 1e-9: # and mean is also very small
                 signal_obj.signal = np.zeros_like(signal_obj.signal) # Make it all zeros
            else: # if mean is not zero but std is, it's a constant signal. Standardizing makes it zero.
                 signal_obj.signal = np.zeros_like(signal_obj.signal) # (X - C) / eps -> huge if not handled. Or just leave as is.
                                                                    # Forcing to zero if std is tiny is a common approach.
        else:
            s_mean = np.mean(signal_obj.signal)
            signal_obj.signal = (signal_obj.signal - s_mean) / s_std
            
    # Your FFT convolution method for detrending/normalization (if needed)
    # I'll rename it slightly to fit the naming convention and make it more generic
    def _fft_convolution_generic(self, signal_obj: Signal, kernel_seconds: float, remove_mean=True, normalize_rms=False):
        if len(signal_obj.signal) == 0: return # Skip empty signals
        
        kernel_size = int(kernel_seconds * signal_obj.frequency)
        if kernel_size <=0: # Ensure kernel size is at least 1
            kernel_size = 1
        
        kernel_size = min(kernel_size, len(signal_obj.signal)) # Kernel cannot be larger than signal

        # Compute and remove moving average with FFT convolution
        if remove_mean:
            moving_avg = fftconvolve(signal_obj.signal, np.ones(shape=(kernel_size,)) / kernel_size, mode="same")
            signal_obj.signal = signal_obj.signal - moving_avg

        # Compute and remove the rms with FFT convolution of squared signal
        if normalize_rms:
            # Ensure signal is not flat zero after mean removal before squaring
            if np.allclose(signal_obj.signal, 0):
                # If signal is all zero, RMS is zero, division by zero. No scaling needed.
                pass
            else:
                temp_sq_signal = np.square(signal_obj.signal)
                moving_sq_avg = fftconvolve(temp_sq_signal, np.ones(shape=(kernel_size,)) / kernel_size, mode="same")

                # Deal with negative values (mathematically, it should never be negative, but fft artifacts can cause this)
                moving_sq_avg[moving_sq_avg < 0] = 0.0

                # Deal with invalid values (though less likely with float64)
                invalid_indices = np.isnan(moving_sq_avg) | np.isinf(moving_sq_avg)
                if np.any(invalid_indices):
                    valid_max = np.max(moving_sq_avg[~invalid_indices]) if np.any(~invalid_indices) else 0.0
                    moving_sq_avg[invalid_indices] = valid_max # Replace NaNs/Infs with max of valid or 0

                scale = np.sqrt(moving_sq_avg)
                scale[(scale < 1e-9) | np.isinf(scale) | np.isnan(scale)] = 1.0 # Avoid division by zero/small RMS
                signal_obj.signal = signal_obj.signal / scale
                
    def fft_detrend_long(self, signal_obj: Signal, record_signal_obj: RecordSignal, kernel_seconds: float):
        """Uses FFT convolution to remove very long-term trends (moving average)."""
        self._fft_convolution_generic(signal_obj, kernel_seconds, remove_mean=True, normalize_rms=False)


    def process_signal(self, signal_data: np.ndarray, initial_frequency: float, signal_type: str):
        if signal_type not in self.config["stepsPerType"]:
            print(f"Warning: No processing steps defined for signal type '{signal_type}'. Skipping.")
            return Signal(signal_data, initial_frequency) # Return original if no steps

        signal_obj = Signal(signal_data.copy(), initial_frequency) # Work on a copy
        record_signal_obj = RecordSignal(self.config["target_frequency"])

        steps_to_apply = self.config["stepsPerType"][signal_type]

        for step_name, params in steps_to_apply:
            method_to_call = getattr(self, step_name, None)
            if method_to_call:
                # print(f"Applying {step_name} with params {params} to {signal_type} (current Fs: {signal_obj.frequency:.2f} Hz)")
                method_to_call(signal_obj, record_signal_obj, **params)
            else:
                print(f"Warning: Preprocessing step '{step_name}' not implemented.")
        
        # print(f"Finished processing {signal_type}. Final Fs: {signal_obj.frequency:.2f} Hz, Length: {len(signal_obj.signal)}")
        return signal_obj