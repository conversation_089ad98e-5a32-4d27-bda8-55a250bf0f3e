{"cells": [{"cell_type": "code", "execution_count": null, "id": "cdf0f5f8", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.11/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[Training] Set the inputshape to [2304000, 10]\n", "\u001b[33;1;4m[Project] Data dataset-validationTSM-3edb8857-f98e6a85-4ff76fed-3edb8857--current was not found, try to find phase to generate it\u001b[0m\n", "[Training] Set the inputshape to [2304000, 10]\n", "\u001b[33;1;4m[Project] Data dataversionmanagerTSM-3edb8857-f98e6a85-4ff76fed-3edb8857--current was not found, try to find phase to generate it\u001b[0m\n", "[Setup] Removing incomplete records from dataversionmanager\n", "[Training] Set the inputshape to [2304000, 10]\n", "\u001b[32;1;4m[DataversionManager] All records are unique and present in the dataset splits\u001b[0m\n", "[Training] Set the inputshape to [2304000, 10]\n", "[Training] Set the inputshape to [2304000, 10]\n", "Optimizer: <PERSON> (\n", "Parameter Group 0\n", "    amsgrad: True\n", "    betas: (0.9, 0.999)\n", "    capturable: False\n", "    differentiable: False\n", "    eps: 1e-08\n", "    foreach: None\n", "    fused: None\n", "    lr: 0.00025\n", "    maximize: False\n", "    weight_decay: 0\n", ")\n", "Loss function:\n", "LossConfig(normalize=None, catLoss='cat', binLoss='bcelogits', lossReduce='uncertainty', UncertaintyWeightLoss=False, label_smoothing=0.0, dice_smooth=1.0, class_weights=None, focalGamma=4, hard_negative_mining=False, hard_negative_ratio=3.0, hard_negative_min_keep=100, hard_negative_strategy='topk', hard_negative_threshold=0.7, ohem_keep_ratio=0.7)\n", "CrossEntropyLoss()\n"]}], "source": ["import phases \n", "import numpy as np\n", "from pyPhasesML import ModelManager\n", "import torch\n", "import os\n", "\n", "configPath = \"logs/TSM-3edb8857-f98e6a85-4ff76fed-3edb8857-SleepPPGNet-eb733d78-7f1c42f1-8a35a439-2128c9d5-0/model.config\"\n", "model_path = \"logs/TSM-3edb8857-f98e6a85-4ff76fed-3edb8857-SleepPPGNet-eb733d78-7f1c42f1-8a35a439-2128c9d5-0/checkpointModel_64_0.734_0.807_0.561_0.400_0.387_543.000_0.644_0.623_0.831_0.727_0.715_289.000.pkl\"\n", "\n", "os.chdir('/app')\n", "project = phases.loadProject('project.yaml', 'local.yml,configs/full.yml,configs/apnea.yml,configs/dsds.yml,configs/dsds-multi.yml')\n", "\n", "data_loader = project.getData(\"dataset-validation\", np.ndarray)\n", "# Load configuration and data\n", "project.setConfig(\"trainingParameter.batchSize\", 1)\n", "project.loadAndApplyConfig(configPath)\n", "\n", "device = 'cuda'\n", "\n", "pyPhaseModelmodel = ModelManager.getModel()\n", "modelState = torch.load(model_path, map_location=device, weights_only=True)\n", "pyPhaseModelmodel.loadState(modelState)\n", "model = pyPhaseModelmodel.model\n", "\n", "testRecord = data_loader[2]\n", "sample_input = testRecord[0]\n", "sample_annotations = testRecord[1]"]}, {"cell_type": "code", "execution_count": null, "id": "18a2bc5a", "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "from torch.utils.hooks import RemovalHandle\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from typing import Dict, List, Tuple\n", "\n", "class LayerAnalyzer:\n", "    def __init__(self, model: nn.<PERSON><PERSON>):\n", "        self.model = model\n", "        self.activations = {}\n", "        self.gradients = {}\n", "        self.hooks = []\n", "        \n", "    def register_hooks(self):\n", "        \"\"\"Register forward and backward hooks for all layers\"\"\"\n", "        def get_activation(name):\n", "            def hook(model, input, output):\n", "                if isinstance(output, (list, tuple)):\n", "                    self.activations[name] = [o.detach().cpu() if torch.is_tensor(o) else o for o in output]\n", "                else:\n", "                    self.activations[name] = output.detach().cpu()\n", "            return hook\n", "            \n", "        def get_gradient(name):\n", "            def hook(model, grad_input, grad_output):\n", "                if isinstance(grad_output, (list, tuple)):\n", "                    self.gradients[name] = [g.detach().cpu() if torch.is_tensor(g) and g is not None else g for g in grad_output]\n", "                else:\n", "                    self.gradients[name] = grad_output[0].detach().cpu() if grad_output[0] is not None else None\n", "            return hook\n", "        \n", "        # Register hooks for each task-specific layer\n", "        for name, module in self.model.named_modules():\n", "            if any(task in name.lower() for task in ['sleep', 'arousal', 'respiratory', 'fc']):\n", "                handle1 = module.register_forward_hook(get_activation(name))\n", "                handle2 = module.register_backward_hook(get_gradient(name))\n", "                self.hooks.extend([handle1, handle2])\n", "    \n", "    def analyze_task_layers(self, input_data: torch.Tensor, task_labels: List[torch.Tensor]) -> Dict:\n", "        \"\"\"Analyze which layers contribute to each task\"\"\"\n", "        self.register_hooks()\n", "        \n", "        # Forward pass\n", "        outputs = self.model(input_data)\n", "        \n", "        task_analysis = {}\n", "        \n", "        # Analyze each task output\n", "        for task_idx, (output, label) in enumerate(zip(outputs, task_labels)):\n", "            task_name = self.get_task_name(task_idx)\n", "            \n", "            # Compute loss for this task\n", "            loss = self.compute_task_loss(output, label, task_idx)\n", "            \n", "            # Backward pass to get gradients\n", "            loss.backward(retain_graph=True)\n", "            \n", "            # Analyze gradient flow\n", "            task_analysis[task_name] = {\n", "                'output_shape': output.shape,\n", "                'contributing_layers': self.analyze_gradient_flow(task_name),\n", "                'activation_patterns': self.analyze_activation_patterns(task_name)\n", "            }\n", "        \n", "        self.cleanup_hooks()\n", "        return task_analysis\n", "    \n", "    def get_task_name(self, task_idx: int) -> str:\n", "        task_names = ['sleep_stages', 'arousal', 'obstructive_apnea', 'mixed_apnea', 'central_apnea', 'hypopnea']\n", "        return task_names[task_idx] if task_idx < len(task_names) else f'task_{task_idx}'\n"]}, {"cell_type": "code", "execution_count": null, "id": "aabf6c54", "metadata": {}, "outputs": [], "source": ["import torch\n", "import numpy as np\n", "from captum.attr import IntegratedGradients, GradientShap, Occlusion\n", "import matplotlib.pyplot as plt\n", "\n", "class ChannelAnalyzer:\n", "    def __init__(self, model: nn.<PERSON>, channel_names: List[str]):\n", "        self.model = model\n", "        self.channel_names = channel_names  # ['EEG_F3', 'EEG_F4', 'EEG_C3', 'EEG_C4', 'EEG_O1', 'EEG_O2', 'EMG', 'EOG_L', 'EOG_R', 'Airflow']\n", "        \n", "    def analyze_channel_importance(self, input_data: torch.Tensor, target_task: int = 0) -> Dict:\n", "        \"\"\"Analyze channel importance for each task using multiple attribution methods\"\"\"\n", "        \n", "        def model_wrapper(x):\n", "            outputs = self.model(x)\n", "            return outputs[target_task]  # Focus on specific task\n", "        \n", "        # Initialize attribution methods\n", "        ig = IntegratedGradients(model_wrapper)\n", "        gs = GradientShap(model_wrapper)\n", "        occlusion = Occlusion(model_wrapper)\n", "        \n", "        results = {}\n", "        \n", "        # Integrated Gradients\n", "        ig_attr = ig.attribute(input_data, n_steps=50)\n", "        results['integrated_gradients'] = self.summarize_channel_attribution(ig_attr)\n", "        \n", "        # Gradient SHAP\n", "        baseline = torch.zeros_like(input_data)\n", "        gs_attr = gs.attribute(input_data, baseline, n_samples=50)\n", "        results['gradient_shap'] = self.summarize_channel_attribution(gs_attr)\n", "        \n", "        # Occlusion analysis\n", "        occlusion_attr = occlusion.attribute(\n", "            input_data,\n", "            sliding_window_shapes=(1, 1, 1000),  # Occlude 1000 time points at once\n", "            strides=(1, 1, 500)\n", "        )\n", "        results['occlusion'] = self.summarize_channel_attribution(occlusion_attr)\n", "        \n", "        return results\n", "    \n", "    def summarize_channel_attribution(self, attribution: torch.Tensor) -> Dict:\n", "        \"\"\"Summarize attribution scores per channel\"\"\"\n", "        # attribution shape: [batch, channels, time]\n", "        channel_importance = torch.mean(torch.abs(attribution), dim=[0, 2])  # Average over batch and time\n", "        \n", "        return {\n", "            'channel_scores': channel_importance.cpu().numpy(),\n", "            'channel_ranking': torch.argsort(channel_importance, descending=True).cpu().numpy(),\n", "            'normalized_scores': (channel_importance / torch.sum(channel_importance)).cpu().numpy()\n", "        }\n", "    \n", "    def plot_channel_importance(self, results: Dict, task_name: str):\n", "        \"\"\"Plot channel importance for different attribution methods\"\"\"\n", "        fig, axes = plt.subplots(1, 3, figsize=(15, 5))\n", "        \n", "        for idx, (method, data) in enumerate(results.items()):\n", "            ax = axes[idx]\n", "            scores = data['normalized_scores']\n", "            \n", "            bars = ax.bar(range(len(self.channel_names)), scores)\n", "            ax.set_xticks(range(len(self.channel_names)))\n", "            ax.set_xticklabels(self.channel_names, rotation=45)\n", "            ax.set_title(f'{method.replace(\"_\", \" \").title()} - {task_name}')\n", "            ax.set_ylabel('Normalized Importance')\n", "            \n", "            # Color bars by importance\n", "            for bar, score in zip(bars, scores):\n", "                bar.set_color(plt.cm.viridis(score))\n", "        \n", "        plt.tight_layout()\n", "        plt.savefig(f'channel_importance_{task_name}.png', dpi=300, bbox_inches='tight')\n", "        plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "23112157", "metadata": {}, "outputs": [], "source": ["import torch\n", "import numpy as np\n", "from scipy import signal\n", "from scipy.stats import pearsonr\n", "import matplotlib.pyplot as plt\n", "\n", "class SleepPatternAnalyzer:\n", "    def __init__(self, model: nn.<PERSON>, fs: int = 256):\n", "        self.model = model\n", "        self.fs = fs\n", "        \n", "    def detect_sleep_spindles(self, eeg_data: torch.Tensor, model_activations: Dict) -> Dict:\n", "        \"\"\"Analyze if model detects sleep spindles (11-15 Hz, 0.5-2s duration)\"\"\"\n", "        \n", "        # Extract EEG channels (assuming C3, C4 are channels 2, 3)\n", "        c3_data = eeg_data[:, 2, :].cpu().numpy()\n", "        c4_data = eeg_data[:, 3, :].cpu().numpy()\n", "        \n", "        results = {}\n", "        \n", "        for channel_name, channel_data in [('C3', c3_data), ('C4', c4_data)]:\n", "            # Detect sleep spindles using traditional method\n", "            spindle_detections = self.traditional_spindle_detection(channel_data)\n", "            \n", "            # Correlate with model activations during N2 sleep\n", "            n2_activations = self.extract_n2_activations(model_activations)\n", "            \n", "            correlation = self.correlate_spindles_with_activations(\n", "                spindle_detections, n2_activations\n", "            )\n", "            \n", "            results[channel_name] = {\n", "                'spindle_count': len(spindle_detections),\n", "                'model_correlation': correlation,\n", "                'spindle_times': spindle_detections\n", "            }\n", "        \n", "        return results\n", "    \n", "    def traditional_spindle_detection(self, eeg_signal: np.ndarray) -> List[Tuple[int, int]]:\n", "        \"\"\"Traditional sleep spindle detection algorithm\"\"\"\n", "        # Bandpass filter 11-15 Hz\n", "        sos = signal.butter(4, [11, 15], btype='band', fs=self.fs, output='sos')\n", "        filtered = signal.sosfilt(sos, eeg_signal, axis=-1)\n", "        \n", "        # Envelope detection\n", "        analytic_signal = signal.hilbert(filtered, axis=-1)\n", "        envelope = np.abs(analytic_signal)\n", "        \n", "        # Smooth envelope\n", "        window_size = int(0.1 * self.fs)  # 100ms window\n", "        smoothed = signal.convolve(envelope, np.ones(window_size)/window_size, mode='same', axis=-1)\n", "        \n", "        # Threshold detection\n", "        threshold = np.percentile(smoothed, 95, axis=-1, keepdims=True)\n", "        \n", "        spindle_detections = []\n", "        for batch_idx in range(smoothed.shape[0]):\n", "            above_threshold = smoothed[batch_idx] > threshold[batch_idx]\n", "            \n", "            # Find continuous regions\n", "            diff = np.diff(above_threshold.astype(int))\n", "            starts = np.where(diff == 1)[0]\n", "            ends = np.where(diff == -1)[0]\n", "            \n", "            # Filter by duration (0.5-2 seconds)\n", "            min_duration = int(0.5 * self.fs)\n", "            max_duration = int(2.0 * self.fs)\n", "            \n", "            for start, end in zip(starts, ends):\n", "                duration = end - start\n", "                if min_duration <= duration <= max_duration:\n", "                    spindle_detections.append((batch_idx, start, end))\n", "        \n", "        return spindle_detections\n", "    \n", "    def detect_k_complexes(self, eeg_data: torch.Tensor, model_activations: Dict) -> Dict:\n", "        \"\"\"Analyze if model detects K-complexes\"\"\"\n", "        # K-complex detection: Sharp negative wave followed by positive component\n", "        # Duration: 0.5-2 seconds, amplitude > 75µV from peak to trough\n", "        \n", "        c3_data = eeg_data[:, 2, :].cpu().numpy()\n", "        \n", "        # Detect K-complexes using morphological features\n", "        k_complex_detections = self.traditional_k_complex_detection(c3_data)\n", "        \n", "        # Correlate with N2 model activations\n", "        n2_activations = self.extract_n2_activations(model_activations)\n", "        correlation = self.correlate_k_complexes_with_activations(\n", "            k_complex_detections, n2_activations\n", "        )\n", "        \n", "        return {\n", "            'k_complex_count': len(k_complex_detections),\n", "            'model_correlation': correlation,\n", "            'k_complex_times': k_complex_detections\n", "        }\n", "    \n", "    def detect_sawtooth_waves(self, eeg_data: torch.Tensor, model_activations: Dict) -> Dict:\n", "        \"\"\"Analyze if model detects sawtooth waves for REM sleep\"\"\"\n", "        # Sawtooth waves: 2-6 Hz, triangular morphology, frontal regions\n", "        \n", "        f3_data = eeg_data[:, 0, :].cpu().numpy()  # F3 channel\n", "        f4_data = eeg_data[:, 1, :].cpu().numpy()  # F4 channel\n", "        \n", "        results = {}\n", "        \n", "        for channel_name, channel_data in [('F3', f3_data), ('F4', f4_data)]:\n", "            # Detect sawtooth waves\n", "            sawtooth_detections = self.traditional_sawtooth_detection(channel_data)\n", "            \n", "            # Correlate with REM model activations\n", "            rem_activations = self.extract_rem_activations(model_activations)\n", "            correlation = self.correlate_sawtooth_with_activations(\n", "                sawtooth_detections, rem_activations\n", "            )\n", "            \n", "            results[channel_name] = {\n", "                'sawtooth_count': len(sawtooth_detections),\n", "                'model_correlation': correlation,\n", "                'sawtooth_times': sawtooth_detections\n", "            }\n", "        \n", "        return results\n"]}, {"cell_type": "code", "execution_count": null, "id": "0c0fcd9d", "metadata": {}, "outputs": [], "source": ["import torch\n", "import numpy as np\n", "from typing import Dict, List, Tuple\n", "import matplotlib.pyplot as plt\n", "\n", "class MultiTaskAnalyzer:\n", "    def __init__(self, model: nn.<PERSON><PERSON>):\n", "        self.model = model\n", "        \n", "    def analyze_plm_arousal_relationship(self, \n", "                                       input_data: <PERSON>.<PERSON><PERSON>,\n", "                                       plm_annotations: np.n<PERSON><PERSON>,\n", "                                       arousal_annotations: np.ndarray) -> Dict:\n", "        \"\"\"Analyze PLM-arousal relationship according to AASM rules\"\"\"\n", "        \n", "        # Get model predictions\n", "        with torch.no_grad():\n", "            outputs = self.model(input_data)\n", "            arousal_pred = torch.sigmoid(outputs[1]).cpu().numpy()  # Assuming arousal is task 1\n", "        \n", "        # Detect PLM sequences with <10s intervals\n", "        plm_sequences = self.detect_plm_sequences(plm_annotations, max_interval=10)\n", "        \n", "        # Analyze arousal scoring according to AASM rules\n", "        results = {}\n", "        \n", "        for seq_idx, plm_sequence in enumerate(plm_sequences):\n", "            # Find arousals within 3 seconds of each PLM\n", "            associated_arousals = []\n", "            \n", "            for plm_time in plm_sequence:\n", "                arousal_window = self.get_time_window(arousal_annotations, plm_time, window=3)\n", "                model_arousal_window = self.get_time_window(arousal_pred, plm_time, window=3)\n", "                \n", "                associated_arousals.append({\n", "                    'plm_time': plm_time,\n", "                    'true_arousal': np.any(arousal_window > 0.5),\n", "                    'model_arousal': np.any(model_arousal_window > 0.5),\n", "                    'arousal_strength': np.max(model_arousal_window)\n", "                })\n", "            \n", "            # Check if model follows AASM rule (only first arousal scored)\n", "            model_follows_aasm = self.check_aasm_compliance(associated_arousals)\n", "            \n", "            results[f'sequence_{seq_idx}'] = {\n", "                'plm_count': len(plm_sequence),\n", "                'associated_arousals': associated_arousals,\n", "                'model_follows_aasm': model_follows_aasm,\n", "                'aasm_compliance_score': self.calculate_aasm_compliance_score(associated_arousals)\n", "            }\n", "        \n", "        return results\n", "    \n", "    def check_aasm_compliance(self, associated_arousals: List[Dict]) -> bool:\n", "        \"\"\"Check if model follows AASM rule for PLM-associated arousals\"\"\"\n", "        if len(associated_arousals) < 2:\n", "            return True\n", "        \n", "        # AASM rule: only first arousal should be scored in sequence\n", "        first_arousal_scored = associated_arousals[0]['model_arousal']\n", "        subsequent_arousals = [ar['model_arousal'] for ar in associated_arousals[1:]]\n", "        \n", "        # Model should score first arousal but suppress subsequent ones\n", "        return first_arousal_scored and not any(subsequent_arousals)\n", "    \n", "    def analyze_respiratory_arousal_coupling(self, \n", "                                           input_data: <PERSON>.<PERSON><PERSON>,\n", "                                           respiratory_events: Dict,\n", "                                           arousal_annotations: np.ndarray) -> Dict:\n", "        \"\"\"Analyze coupling between respiratory events and arousals\"\"\"\n", "        \n", "        with torch.no_grad():\n", "            outputs = self.model(input_data)\n", "            arousal_pred = torch.sigmoid(outputs[1]).cpu().numpy()\n", "            resp_preds = {\n", "                'obstructive': torch.sigmoid(outputs[2]).cpu().numpy(),\n", "                'mixed': torch.sigmoid(outputs[3]).cpu().numpy(), \n", "                'central': torch.sigmoid(outputs[4]).cpu().numpy(),\n", "                'hypopnea': torch.sigmoid(outputs[5]).cpu().numpy()\n", "            }\n", "        \n", "        coupling_analysis = {}\n", "        \n", "        for event_type, pred in resp_preds.items():\n", "            # Find respiratory events\n", "            event_times = self.detect_events(pred, threshold=0.5)\n", "            \n", "            # Analyze arousal coupling\n", "            coupled_events = 0\n", "            total_events = len(event_times)\n", "            \n", "            coupling_strengths = []\n", "            \n", "            for event_time in event_times:\n", "                # Check for arousal within 3 seconds after event end\n", "                post_event_window = self.get_time_window(\n", "                    arousal_pred, event_time, window=3, offset=0\n", "                )\n", "                \n", "                max_arousal = np.max(post_event_window) if len(post_event_window) > 0 else 0\n", "                coupling_strengths.append(max_arousal)\n", "                \n", "                if max_arousal > 0.5:\n", "                    coupled_events += 1\n", "            \n", "            coupling_analysis[event_type] = {\n", "                'total_events': total_events,\n", "                'coupled_events': coupled_events,\n", "                'coupling_rate': coupled_events / total_events if total_events > 0 else 0,\n", "                'mean_coupling_strength': np.mean(coupling_strengths),\n", "                'coupling_distribution': np.histogram(coupling_strengths, bins=10)\n", "            }\n", "        \n", "        return coupling_analysis\n", "    \n", "    def analyze_sleep_stage_event_relationships(self, \n", "                                              input_data: <PERSON>.<PERSON><PERSON>,\n", "                                              sleep_annotations: np.n<PERSON>ray) -> Dict:\n", "        \"\"\"Analyze how events are distributed across sleep stages\"\"\"\n", "        \n", "        with torch.no_grad():\n", "            outputs = self.model(input_data)\n", "            sleep_pred = torch.softmax(outputs[0], dim=-1).cpu().numpy()\n", "            \n", "            event_preds = {\n", "                'arousal': torch.sigmoid(outputs[1]).cpu().numpy(),\n", "                'obstructive': torch.sigmoid(outputs[2]).cpu().numpy(),\n", "                'mixed': torch.sigmoid(outputs[3]).cpu().numpy(),\n", "                'central': torch.sigmoid(outputs[4]).cpu().numpy(),\n", "                'hypopnea': torch.sigmoid(outputs[5]).cpu().numpy()\n", "            }\n", "        \n", "        stage_names = ['Wake', 'N1', 'N2', 'N3', 'REM']\n", "        stage_event_analysis = {}\n", "        \n", "        for stage_idx, stage_name in enumerate(stage_names):\n", "            # Find periods of this sleep stage\n", "            stage_mask = np.argmax(sleep_pred, axis=-1) == stage_idx\n", "            \n", "            stage_analysis = {'stage_name': stage_name, 'events': {}}\n", "            \n", "            for event_type, event_pred in event_preds.items():\n", "                # Count events during this stage\n", "                stage_events = event_pred[stage_mask]\n", "                event_count = np.sum(stage_events > 0.5)\n", "                event_rate = event_count / np.sum(stage_mask) if np.sum(stage_mask) > 0 else 0\n", "                \n", "                stage_analysis['events'][event_type] = {\n", "                    'count': int(event_count),\n", "                    'rate_per_epoch': event_rate,\n", "                    'mean_probability': np.mean(stage_events)\n", "                }\n", "            \n", "            stage_event_analysis[stage_name] = stage_analysis\n", "        \n", "        return stage_event_analysis\n", "    \n", "    def analyze_task_interference(self, input_data: torch.Tensor) -> Dict:\n", "        \"\"\"Analyze potential interference between tasks\"\"\"\n", "        \n", "        # Get gradients for each task\n", "        task_gradients = {}\n", "        \n", "        with torch.enable_grad():\n", "            input_data.requires_grad_(True)\n", "            outputs = self.model(input_data)\n", "            \n", "            for task_idx, output in enumerate(outputs):\n", "                # Compute gradient of each task w.r.t. input\n", "                task_loss = torch.mean(output)  # Simple aggregation\n", "                grad = torch.autograd.grad(\n", "                    task_loss, input_data, \n", "                    retain_graph=True, create_graph=False\n", "                )[0]\n", "                task_gradients[f'task_{task_idx}'] = grad.detach()\n", "        \n", "        # Compute gradient similarities between tasks\n", "        task_names = ['sleep_stages', 'arousal', 'obstructive', 'mixed', 'central', 'hypopnea']\n", "        similarity_matrix = np.zeros((len(task_names), len(task_names)))\n", "        \n", "        for i, task_i in enumerate(task_names):\n", "            for j, task_j in enumerate(task_names):\n", "                if i <= j:\n", "                    grad_i = task_gradients[f'task_{i}'].flatten()\n", "                    grad_j = task_gradients[f'task_{j}'].flatten()\n", "                    \n", "                    # Cosine similarity\n", "                    similarity = torch.cosine_similarity(grad_i, grad_j, dim=0)\n", "                    similarity_matrix[i, j] = similarity_matrix[j, i] = similarity.item()\n", "        \n", "        return {\n", "            'gradient_similarity_matrix': similarity_matrix,\n", "            'task_names': task_names,\n", "            'highly_correlated_pairs': self.find_correlated_task_pairs(similarity_matrix, task_names)\n", "        }\n", "    \n", "    def detect_plm_sequences(self, plm_annotations: np.n<PERSON>ray, max_interval: int = 10) -> List[List[int]]:\n", "        \"\"\"Detect PLM sequences with intervals < max_interval seconds\"\"\"\n", "        plm_times = np.where(plm_annotations > 0)[0]\n", "        \n", "        if len(plm_times) == 0:\n", "            return []\n", "        \n", "        sequences = []\n", "        current_sequence = [plm_times[0]]\n", "        \n", "        for i in range(1, len(plm_times)):\n", "            interval = plm_times[i] - plm_times[i-1]\n", "            \n", "            if interval <= max_interval:\n", "                current_sequence.append(plm_times[i])\n", "            else:\n", "                if len(current_sequence) >= 2:  # Only sequences with 2+ PLMs\n", "                    sequences.append(current_sequence)\n", "                current_sequence = [plm_times[i]]\n", "        \n", "        # Don't forget the last sequence\n", "        if len(current_sequence) >= 2:\n", "            sequences.append(current_sequence)\n", "        \n", "        return sequences\n", "    \n", "    def get_time_window(self, data: np.ndarray, center_time: int, window: int, offset: int = 0) -> np.ndarray:\n", "        \"\"\"Extract time window around center_time\"\"\"\n", "        start_time = max(0, center_time + offset)\n", "        end_time = min(len(data), center_time + offset + window)\n", "        return data[start_time:end_time]\n", "    \n", "    def find_correlated_task_pairs(self, similarity_matrix: np.n<PERSON><PERSON>, task_names: List[str], threshold: float = 0.7) -> List[Tuple[str, str, float]]:\n", "        \"\"\"Find highly correlated task pairs\"\"\"\n", "        correlated_pairs = []\n", "        \n", "        for i in range(len(task_names)):\n", "            for j in range(i+1, len(task_names)):\n", "                similarity = similarity_matrix[i, j]\n", "                if abs(similarity) > threshold:\n", "                    correlated_pairs.append((task_names[i], task_names[j], similarity))\n", "        \n", "        return sorted(correlated_pairs, key=lambda x: abs(x[2]), reverse=True)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "8b0fc6f8", "metadata": {}, "outputs": [], "source": ["class ComprehensiveModelAnalyzer:\n", "    def __init__(self, model: str, channel_names: List[str]):\n", "        self.model = model\n", "        self.channel_names = channel_names\n", "        \n", "        # Initialize all analyzers\n", "        self.layer_analyzer = LayerAnalyzer(self.model)\n", "        self.channel_analyzer = ChannelAnalyzer(self.model, channel_names)\n", "        self.pattern_analyzer = SleepPatternAnalyzer(self.model)\n", "        self.multitask_analyzer = MultiTaskAnalyzer(self.model)\n", "        # self.additional_analyzer = AdditionalAnalyzer(self.model)\n", "    \n", "    def run_complete_analysis(self, test_data: torch.Tensor, annotations: Dict) -> Dict:\n", "        \"\"\"Run complete model analysis\"\"\"\n", "        \n", "        print(\"Starting comprehensive model analysis...\")\n", "        \n", "        results = {\n", "            'model_info': self.get_model_info(),\n", "            'layer_analysis': {},\n", "            'channel_analysis': {},\n", "            'pattern_analysis': {},\n", "            'multitask_analysis': {},\n", "            'additional_analysis': {}\n", "        }\n", "        \n", "        # 1. Layer usage analysis\n", "        print(\"Analyzing layer usage for each task...\")\n", "        results['layer_analysis'] = self.layer_analyzer.analyze_task_layers(\n", "            test_data, annotations['labels']\n", "        )\n", "        \n", "        # 2. Channel importance analysis\n", "        print(\"Analyzing channel importance...\")\n", "        for task_idx in range(len(annotations['labels'])):\n", "            task_name = self.get_task_name(task_idx)\n", "            results['channel_analysis'][task_name] = self.channel_analyzer.analyze_channel_importance(\n", "                test_data, task_idx\n", "            )\n", "        \n", "        # 3. Sleep pattern analysis\n", "        print(\"Analyzing sleep pattern detection...\")\n", "        results['pattern_analysis'] = {\n", "            'sleep_spindles': self.pattern_analyzer.detect_sleep_spindles(test_data, results['layer_analysis']),\n", "            'k_complexes': self.pattern_analyzer.detect_k_complexes(test_data, results['layer_analysis']),\n", "            'sawtooth_waves': self.pattern_analyzer.detect_sawtooth_waves(test_data, results['layer_analysis'])\n", "        }\n", "        \n", "        # 4. Multi-task relationship analysis\n", "        print(\"Analyzing multi-task relationships...\")\n", "        results['multitask_analysis'] = {\n", "            'plm_arousal': self.multitask_analyzer.analyze_plm_arousal_relationship(\n", "                test_data, annotations.get('plm', np.array([])), annotations.get('arousal', np.array([]))\n", "            ),\n", "            'respiratory_arousal': self.multitask_analyzer.analyze_respiratory_arousal_coupling(\n", "                test_data, annotations.get('respiratory', {}), annotations.get('arousal', np.array([]))\n", "            ),\n", "            'stage_event_relationships': self.multitask_analyzer.analyze_sleep_stage_event_relationships(\n", "                test_data, annotations.get('sleep_stages', np.array([]))\n", "            ),\n", "            'task_interference': self.multitask_analyzer.analyze_task_interference(test_data)\n", "        }\n", "        \n", "        # 5. Additional analysis\n", "        print(\"Running additional interpretability analysis...\")\n", "        # results['additional_analysis'] = self.additional_analyzer.analyze_additional_patterns(\n", "            # test_data, annotations\n", "        # )\n", "        \n", "        return results\n", "    \n", "    def generate_report(self, results: Dict, output_path: str = \"model_analysis_report.html\"):\n", "        \"\"\"Generate comprehensive HTML report\"\"\"\n", "        # Implementation for report generation\n", "        pass\n", "\n", "\n", "channel_names = [\n", "    'EEG',\n", "    'EOG', \n", "    'E<PERSON> Chin', \n", "    'EMG Leg', \n", "    'Position', \n", "    'Snore', \n", "    'Chest Effort', \n", "    'Abdominal Effort', \n", "    'SpO2',  \n", "    'Airflow'\n", "]\n", "\n", "analyzer = ComprehensiveModelAnalyzer(model, channel_names)\n", "\n", "# Load test data and annotations\n", "X, Y = data_loader[2]\n", "X = X[0, :, :]\n", "Y = Y[0, :, :]\n", "annotations = {\n", "    'labels': [\n", "        torch.randint(0, 5, (1, 1200)),  # Sleep stages (5 classes, 1200 epochs)\n", "        torch.randint(0, 2, (1, 36000)),  # <PERSON><PERSON>al (binary, 1s resolution)\n", "        torch.randint(0, 2, (1, 36000)),  # Obstructive apnea\n", "        torch.randint(0, 2, (1, 36000)),  # Mixed apnea  \n", "        torch.randint(0, 2, (1, 36000)),  # Central apnea\n", "        torch.randint(0, 2, (1, 36000)),  # Hypopnea\n", "    ],\n", "    'sleep_stages': np.random.randint(0, 5, 1200),\n", "    'arousal': np.random.randint(0, 2, 36000),\n", "    'plm': np.random.randint(0, 2, 36000),\n", "    'respiratory': {\n", "        'obstructive': np.random.randint(0, 2, 36000),\n", "        'mixed': np.random.randint(0, 2, 36000),\n", "        'central': np.random.randint(0, 2, 36000),\n", "        'hypopnea': np.random.randint(0, 2, 36000)\n", "    }\n", "}\n", "\n", "# Run analysis\n", "results = analyzer.run_complete_analysis(X, Y)\n", "\n", "# Generate report\n", "analyzer.generate_report(results)\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}