from .SleepMultiLoss import LossConfig, SleepMultiLoss
import torch
from torch import nn
import torch.nn.functional as F

from pyPhasesML.scorer.ScorerTorch import ScorerTorch
from pyPhasesML.adapter.ModelTorchAdapter import ModelTorchAdapter
from SleePyPhases.MultiScorer import MultiScorer


from src.ReduceLROnPlateau import ReduceLROnPlateau


from .utils import get_norm, get_activation



class CustomBCELoss(nn.Module):
    def __init__(self):
        super(CustomBCELoss, self).__init__()
        self.loss = nn.BCEWithLogitsLoss()

    def forward(self, y_hat, y):
        y_hat = y_hat.reshape(-1)
        y = y.view(-1)

        y_hat = y_hat[y > -0.5]
        y = y[y > -0.5]

        return self.loss(y_hat, y.float())

class SqueezeExcitationBlock(nn.Module):
    def __init__(self, channels, reduction_ratio=16):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool1d(1)
        reduced_channels = max(channels // reduction_ratio, 1)

        self.fc = nn.Sequential(
            nn.Linear(channels, reduced_channels), nn.ReLU(inplace=True), nn.Linear(reduced_channels, channels), nn.Sigmoid()
        )

    def forward(self, x):
        batch_size, channels, _ = x.size()

        # Squeeze
        y = self.avg_pool(x).view(batch_size, channels)

        # Excitation
        y = self.fc(y).view(batch_size, channels, 1)

        # Scale
        return x * y.expand_as(x)


class ResConvBlock(nn.Module):
    def __init__(
        self,
        in_channels,
        out_channels,
        kernel_size=3,
        stride=1,
        pool_size=2,
        norm="batch",
        activation="leaky",
        se_reduction=16,  # New parameter for SE block
    ):
        super().__init__()
        self.conv1 = nn.Conv1d(in_channels, out_channels, kernel_size, stride, padding=kernel_size // 2)
        self.norm1 = get_norm(norm, num_features=out_channels)

        self.conv2 = nn.Conv1d(out_channels, out_channels, kernel_size, stride, padding=kernel_size // 2)
        self.norm2 = get_norm(norm, num_features=out_channels)

        self.conv3 = nn.Conv1d(out_channels, out_channels, kernel_size, stride, padding=kernel_size // 2)
        self.norm3 = get_norm(norm, num_features=out_channels)

        # Add SE block
        if se_reduction is not None:
            self.se = SqueezeExcitationBlock(out_channels, reduction_ratio=se_reduction)
        else:
            self.se = None

        self.pool = nn.MaxPool1d(pool_size, stride=pool_size)
        self.activation = get_activation(activation)

        # If input and output dimensions don't match, use a 1x1 conv to match dimensions
        self.match_dimensions = nn.Conv1d(
            in_channels,
            out_channels,
            1,
            stride=pool_size,
            padding=0,
            bias=False,
        )

    def forward(self, x):
        identity = self.match_dimensions(x)

        out = self.conv1(x)
        out = self.norm1(out)
        out = self.activation(out)

        out = self.conv2(out)
        out = self.norm2(out)
        out = self.activation(out)

        out = self.conv3(out)
        out = self.norm3(out)

        if self.se is not None:
            out = self.se(out)

        # Add residual connection
        out = self.activation(out)

        # Apply max pooling
        out = self.pool(out)
        out = out + identity

        return out


class TCNBlock(nn.Module):
    def __init__(
        self,
        channels,
        kernel_size=7,
        dilation_rates=[1, 2, 4, 8, 16, 32],
        dropout=0.2,
        norm="batch",
        activation="leaky",
    ):
        super().__init__()
        self.dilated_convs = nn.ModuleList()
        self.norms = nn.ModuleList()

        for rate in dilation_rates:
            padding = (kernel_size - 1) * rate // 2
            dilated_conv = nn.Conv1d(channels, channels, kernel_size, padding=padding, dilation=rate)
            self.dilated_convs.append(dilated_conv)
            self.norms.append(get_norm(norm, num_features=channels))

        self.activation = get_activation(activation)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        identity = x

        out = x
        for i, conv in enumerate(self.dilated_convs):
            out = conv(out)
            out = self.norms[i](out)
            out = self.activation(out)

        # Residual connection
        out = out + identity
        out = self.dropout(out)
        out = self.activation(out)

        return out


class WindowLayer(nn.Module):
    def __init__(self, input_size, num_windows, window_size):
        super().__init__()
        self.num_windows = num_windows
        self.window_size = window_size

    def forward(self, x):
        # x shape: [batch_size, channels, length]
        batch_size, channels, _ = x.size()

        # Reshape to create windows
        x = x.view(batch_size, channels, self.num_windows, -1)
        x = x.permute(0, 2, 1, 3)  # [batch_size, num_windows, channels, window_size]
        x = x.reshape(batch_size, self.num_windows, -1)

        return x


class TimeDistributedDense(nn.Module):
    def __init__(self, input_dim, output_dim, activation="leaky", norm="batch"):
        super().__init__()
        self.dense = nn.Linear(input_dim, output_dim)
        self.activation = get_activation(activation)
        self.norm = get_norm(norm, num_features=output_dim) if norm else nn.Identity()

    def forward(self, x):
        # x shape: [batch_size, time_steps, input_dim]
        batch_size, time_steps, input_dim = x.size()

        # Reshape to apply the same dense layer to each timestep
        x_reshaped = x.reshape(batch_size, -1, input_dim)
        # x_reshaped = x.reshape(batch_size, -1, input_dim)
        y = self.dense(x_reshaped)

        # Apply normalization if needed (need to reshape for 1D norm)
        if not isinstance(self.norm, nn.Identity):
            y = y.view(batch_size, time_steps, -1).transpose(1, 2)  # [B, output_dim, time_steps]
            y = self.norm(y)
            y = y.transpose(1, 2)  # [B, time_steps, output_dim]
        else:
            y = y.view(batch_size, time_steps, -1)

        y = self.activation(y)

        return y


class SleepPPGNetTorch(nn.Module):
    def __init__(
        self,
        input_size,
        num_classes,
        norm="batch",
        activation="leaky",
        se_reduction=None,
        multiClassNums=None,  # Dictionary of micro event types and their class counts
        micro_window_size=1,  # Size of micro event windows in seconds (default: 1 second)
        base_window_size=30,  # Size of sleep stage windows in seconds (default: 30 seconds)
        input_channels=1,
        sleep=True,
        events=False,
        event_tcn_delations=[1, 2, 4, 8],
        sleep_tcn_delations=[1, 2, 4, 8, 16, 32],
        downsampling_channels=[1, 16, 16, 32, 32, 64, 64, 128, 256],
        sleep_dense_out = 128,
        event_dense_out = 240,
        fe_kernel = 3,
        tcn_kernel = 7,
    ):
        super().__init__()

        num_sleep_classes = 4
        self.input_size = input_size
        self.multiClassNums = multiClassNums or [4]
        self.micro_window_size = micro_window_size
        self.base_window_size = base_window_size
        self.windows_per_epoch = base_window_size // micro_window_size if micro_window_size > 0 else 1
        self.sleep = sleep
        self.events = events

        # Feature extractor (FE) - 8 stacked ResConvs
        self.fe_layers = nn.ModuleList()
        # channels = [1, 16, 16, 32, 32, 64, 64]
        channels = downsampling_channels
        channels[0] = input_channels

        for i in range(len(channels) - 1):
            self.fe_layers.append(
                ResConvBlock(
                    channels[i],
                    channels[i + 1],
                    kernel_size=fe_kernel,
                    norm=norm,
                    activation=activation,
                    se_reduction=se_reduction,
                )
            )

        # Calculate the output size after FE layers (8 times pooling by 2)
        self.fe_output_size = input_size // (2**8)
        self.fe_output_channels = channels[-1]


        # Windowing layer for sleep stages (30s)
        # self.sleep_window_size = 160
        self.sleep_num_windows = 1200  # 10 hours of 30s windows
        self.sleep_window_size = 8
        self.sleep_window_layer = WindowLayer(input_size // 256, self.sleep_num_windows, self.sleep_window_size)

        # For micro events - create a separate upsampling path
        
        # We need to upscale by a factor of 30 (from 30s to 1s resolution)
        # 2^4 = 16, so we'll use 4 upsampling layers of factor 2, then a final layer of factor ~2
        self.micro_upsample_layers = nn.ModuleList()
        
        # Define upsampling channels
        up_channels = [self.fe_output_channels, 256, 256]
        # up_channels = [self.fe_output_channels]
        self.fe_micro_output_size = input_size // (2**(8+len(up_channels)))
        self.fe_micro_output_channel = up_channels[-1]
        upsamplingKernel = 3
        
        # Create 4 upsampling layers (each with factor 2)
        for i in range(len(up_channels)-1):
            self.micro_upsample_layers.append(
                nn.Sequential(
                    nn.ConvTranspose1d(
                        up_channels[i], 
                        up_channels[i+1], 
                        kernel_size=upsamplingKernel, 
                        stride=2,
                        padding=1,
                        output_padding=1,
                    ),
                    get_norm(norm, num_features=up_channels[i+1]),
                    get_activation(activation)
                )
            )
        
        # Micro event feature size after upsampling
        self.micro_feature_size = up_channels[-1]
        self.micro_num_windows = self.sleep_num_windows * self.windows_per_epoch

        # self.micro_window_size = 1 # ~1 sek
        self.micro_num_windows = 36000
        self.micro_window_layer = WindowLayer(self.fe_micro_output_size, self.micro_num_windows, self.micro_window_size)

        self.micro_td_dense = TimeDistributedDense(self.fe_micro_output_channel, event_dense_out, activation=activation, norm=norm)
        self.micro_tcn1 = TCNBlock(event_dense_out, kernel_size=tcn_kernel, dilation_rates=event_tcn_delations, norm=norm, activation=activation)
        self.micro_tcn2 = TCNBlock(event_dense_out, kernel_size=tcn_kernel, dilation_rates=event_tcn_delations, norm=norm, activation=activation)

        # self.event_window = WindowLayer(input_size // 256, self.sleep_num_windows, self.sleep_window_size)
        # Time-distributed dense layers
        self.sleep_td_dense = TimeDistributedDense(self.sleep_window_size * self.fe_output_channels, sleep_dense_out, activation=activation, norm=norm)


        # TCN blocks for sleep staging
        self.sleep_tcn1 = TCNBlock(sleep_dense_out, dilation_rates=sleep_tcn_delations, norm=norm, activation=activation)
        self.sleep_tcn2 = TCNBlock(sleep_dense_out, dilation_rates=sleep_tcn_delations, norm=norm, activation=activation)

        # Output heads
        # For micro events (one head per event type)
        self.fcs = nn.ModuleList()
        for i, event_classes in enumerate(self.multiClassNums):
            dense_out = sleep_dense_out if sleep and i == 0 else event_dense_out
            self.fcs.append(nn.Conv1d(dense_out, event_classes, kernel_size=1))

    def forward(self, x):
        # x shape: [batch_size, 1, input_size]
        
        # Apply feature extraction
        for layer in self.fe_layers:
            x = layer(x)
        
        # Store the shared features
        shared_features = x  # [batch_size, channels, length]

        output = []

        if self.sleep:
            sleep_x = F.interpolate(
                shared_features, 
                size=9600,
                mode='linear', 
                align_corners=False
            )
            
            # Process for sleep staging (30s windows)
            sleep_x = self.sleep_window_layer(sleep_x)  # [batch_size, sleep_num_windows, window_features]
            sleep_x = self.sleep_td_dense(sleep_x)  # [batch_size, sleep_num_windows, 128]
            sleep_x = sleep_x.transpose(1, 2)  # [batch_size, 128, sleep_num_windows]
            sleep_x = self.sleep_tcn1(sleep_x)
            sleep_x = self.sleep_tcn2(sleep_x)
            sleep_output = self.fcs[0](sleep_x)  # [batch_size, num_classes, sleep_num_windows]
            
            # # If no micro event classes defined, return only sleep staging output
            if not self.events:
                return [sleep_output.transpose(1,2)]
            
            output.append(sleep_output.transpose(1,2))
            
        # Process for micro events using upsampling path
        micro_x = shared_features
        
        # Apply upsampling layers
        for layer in self.micro_upsample_layers:
            micro_x = layer(micro_x)
                
        # Apply convolution to get features for micro events
        micro_x = self.micro_window_layer(micro_x) 
        micro_x = self.micro_td_dense(micro_x)  # [batch_size, sleep_num_windows, 128]
        micro_x = micro_x.transpose(1, 2)  # [batch_size, 128, sleep_num_windows]
        
        # Apply TCN blocks
        micro_x = self.micro_tcn1(micro_x)
        micro_x = self.micro_tcn2(micro_x)
        
        # Generate outputs for each micro event type
        startAt = 1 if self.sleep else 0
        for fc in self.fcs[startAt:]:
            output.append(fc(micro_x).transpose(1,2))  # [batch_size, event_classes, micro_num_windows]

        return output


class SleepPPGNet(ModelTorchAdapter):
    def initialOptions(self):
        return {
            "micro_event_classes": {},  # Dictionary of micro event types and their class counts
            "micro_window_size": 1,  # Size of micro event windows in seconds (default: 1 second)
            "base_window_size": 30,  # Size of sleep stage windows in seconds (default: 30 seconds)
            "task_weights": None,  # Weights for different tasks in multi-task learning
            "event_tcn_delations": [1, 2, 4, 8],
            "sleep_tcn_delations": [1, 2, 4, 8, 16, 32],
            "downsampling_channels": [1, 16, 16, 32, 32, 64, 64, 128, 256],
            "sleep_dense_out":  128,
            "event_dense_out":  240,
            "fe_kernel": 3,
            "tcn_kernel": 7,
        }

    def define(self):
        # Setup for sleep stages
        self.config.numClasses = len(self.classNames[0])
        self.numClasses = len(self.classNames[0])

        # Get micro event configuration
        self.micro_window_size = self.getOption("micro_window_size")
        self.base_window_size = self.getOption("base_window_size")
        self.task_weights = self.getOption("task_weights")
        self.multiClassNums = self.getOption("multiclassificationSize") if "multiclassificationSize" in self.options else False
        self.lossOptions = LossConfig.fromDict(self.getOption("lossOptions"))

        torch.set_float32_matmul_precision('high')

        self.model = SleepPPGNetTorch(
            num_classes=self.numClasses,
            input_size=self.inputShape[0],
            input_channels=self.inputShape[1],
            se_reduction=self.getOption("se_reduction"),
            multiClassNums=self.multiClassNums,
            micro_window_size=self.micro_window_size,
            base_window_size=self.base_window_size,
            sleep=self.getOption("sleep"),
            events=self.getOption("events"),
            event_tcn_delations=self.getOption("event_tcn_delations"),
            sleep_tcn_delations=self.getOption("sleep_tcn_delations"),
            downsampling_channels=self.getOption("downsampling_channels"),
            sleep_dense_out=self.getOption("sleep_dense_out"),
            event_dense_out=self.getOption("event_dense_out"),
            fe_kernel=self.getOption("fe_kernel"),
            tcn_kernel=self.getOption("tcn_kernel"),
        )
        
        self.optimizer = torch.optim.Adam(
            self.model.parameters(),
            lr=self.config.learningRate,
            weight_decay=self.config.learningRateDecay,
            betas=(0.9, 0.999),
            eps=1e-8,
            amsgrad=True,  # from git implementation
        )
        print(f"Optimizer: {self.optimizer}")

        
        # if hasattr(torch, 'compile'):
        #     self.model = torch.compile(self.model, mode="max-autotune")

        reduceLROptions = self.getOption("reduceLROnPlateau")
        reduce_lr = ReduceLROnPlateau(
            self.config,
            monitor=reduceLROptions["monitor"],
            patience=reduceLROptions["patience"],
            factor=reduceLROptions["factor"],
            min_lr=1e-7,
        )
        self.registerCB(reduce_lr)
        
        if self.useGPU:
            self.model.cuda()
            
        self.lossDimension = self.config.numClasses if self.oneHotDecoded else 1

        l = SleepMultiLoss(self.multiClassNums, config=self.lossOptions, model=self.model)
        optimGroups = []

        if self.lossOptions.lossReduce in ["uncertainty", "gradnorm", "uncertainty_gradnorm"]:
            optimGroups.append({'params': self.model.parameters()})
            
            if self.lossOptions.lossReduce in ["gradnorm", "uncertainty_gradnorm"]:
                optimGroups.append({'params': l.gradnorm.parameters()})

            if self.lossOptions.lossReduce in ["uncertainty", "uncertainty_gradnorm"]:
                optimGroups.append({'params': l.uncertainty_weight.parameters()})


            self.optimizer = torch.optim.Adam(optimGroups, lr=self.config.learningRate)

        self.lossCriterium = l
        print("Loss function:")
        print(self.lossOptions)
        print(l.loss)



    def prepareY(self, Y):
        if self.useGPU:
            Y = [y.cuda() for y in Y]
        return Y
    
    def getLossFunction(self):
        return self.lossCriterium

    def mapOutput(self, output):
        return output  # for own

    def mapOutputForLoss(self, output, mask=None):
        output = self.mapOutput(output)
        return output

    def mapOutputForPrediction(self, output):
        return self.mapOutput(output)

    def prepareForScore(self, targets, prediction):
        # prediction = prediction.permute(0, 2, 1)
        return targets, prediction

    def getScorer(self):
        return MultiScorer(self.multiClassNums, self.validationMetrics, scorerNames=self.config.labelNames, ignoreClasses=[self.ignoreClassIndex], scorerClasses=[ScorerTorch for _ in range(self.config.numClasses)])
