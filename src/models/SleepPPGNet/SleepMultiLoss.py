from dataclasses import dataclass
import torch
import torch.nn as nn

from ..utilPyTorch.Basics import ModelConfig
import torch.nn.functional as F

@dataclass
class LossConfig(ModelConfig):
    normalize: str = "None"
    catLoss: str = "crossentropy"
    binLoss: str = "bce"
    lossReduce: str = "mean"
    UncertaintyWeightLoss: bool = False
    label_smoothing: float = 0.0
    dice_smooth: float = 1.0
    class_weights: list[float] = None
    focalGamma: float = 2.0
    
    # Hard negative mining options
    hard_negative_mining: bool = False
    hard_negative_ratio: float = 3.0  # ratio of negatives to positives
    hard_negative_min_keep: int = 100  # minimum number of samples to keep
    hard_negative_strategy: str = "topk"  # "topk", "threshold", "ohem"
    hard_negative_threshold: float = 0.7  # for threshold strategy
    ohem_keep_ratio: float = 0.7  # for OHEM strategy

class SleepMultiLoss(nn.Module):
    def __init__(self, multiClassNums, config=LossConfig, model=None):
        super().__init__()
        self.multiClassNums = multiClassNums
        self.reduce = config.lossReduce
        self.model = model
        self.config = config

        match config.catLoss:
            case "bce":
                self.loss = nn.BCELoss(reduction='none')
            case "focal":
                self.loss = FocalTemporalBalanceCrossEntropyLoss(5, gamma=config.focalGamma)
            case "dice":
                self.loss = DiceLoss(smooth=config.dice_smooth, ignore_index=-1, multi_class=True)
            # case "adaptiveSoftMaxWithLoss":
                # self.loss = nn.AdaptiveLogSoftmaxWithLoss(in_features=4, n_classes=4, )
            case _:
                weights = torch.tensor(config.class_weights) if config.class_weights is not None else None
                self.loss = nn.CrossEntropyLoss(ignore_index=-1, label_smoothing=config.label_smoothing, weight=weights)
                
                # weights = torch.tensor(config.class_weights) if config.class_weights is not None else None
                # reduction = 'none' if config.hard_negative_mining else 'mean'
                # self.loss = nn.CrossEntropyLoss(ignore_index=-1, label_smoothing=config.label_smoothing, 
                #                               weight=weights, reduction=reduction)

        match config.binLoss:
            case "bce":
                self.binLoss = nn.BCELoss(reduction='none' if config.hard_negative_mining else 'mean')
            case "bcelogits":
                self.binLoss = nn.BCEWithLogitsLoss(reduction='none' if config.hard_negative_mining else 'mean')
            case "bce-focal":
                self.binLoss = FocalBCEWithLogitsLoss()
            case "dice":
                self.binLoss = DiceLoss(smooth=config.dice_smooth, ignore_index=-1, multi_class=False)
            case _:
                reduction = 'none' if config.hard_negative_mining else 'mean'
                self.binLoss = nn.CrossEntropyLoss(ignore_index=-1, reduction=reduction)

        match config.normalize:
            case "logsoftmax":
                self.normalize = nn.LogSoftmax()
            case "softmax":
                self.normalize = nn.Softmax()
            case _:
                self.normalize = None

        # self.reduce = "same"

        if self.reduce in ["gradnorm", "uncertainty_gradnorm"]:
            gradnorm = GradNorm(n_tasks=len(multiClassNums), alpha=1.5)

            # Move to device
            self.shared_params = []  # Add this line before the loop
            device = next(self.model.parameters()).device
            self.gradnorm = gradnorm.to(device)
            self.shared_params = [p for n, p in self.model.named_parameters() if not n.startswith("sleep") and not n.startswith("fcs")]
            print(f"Selected {len(self.shared_params)} shared parameters for GradNorm")

        if self.reduce in ["uncertainty", "uncertainty_gradnorm"]:
            uncertainty_weight = UncertaintyWeightLoss(n_tasks=len(multiClassNums))
            self.uncertainty_weight = uncertainty_weight.cuda()

        # if self

    def apply_hard_negative_mining(self, loss_per_sample, targets, class_count):
        """
        Apply hard negative mining to focus on difficult examples.
        
        Args:
            loss_per_sample: Per-sample loss tensor
            targets: Ground truth targets
            class_count: Number of classes for this task
            
        Returns:
            Reduced loss tensor after hard negative mining
        """
        if not self.config.hard_negative_mining:
            return loss_per_sample.mean()
            
        if class_count <= 2:  # Binary classification
            return self._apply_binary_hard_negative_mining(loss_per_sample, targets)
        else:  # Multi-class classification
            return self._apply_multiclass_hard_negative_mining(loss_per_sample, targets)
    
    def _apply_binary_hard_negative_mining(self, loss_per_sample, targets):
        """Apply hard negative mining for binary classification."""
        targets_flat = targets.reshape(-1)
        
        # Separate positive and negative samples
        pos_mask = targets_flat == 1
        neg_mask = targets_flat == 0
        
        pos_losses = loss_per_sample[pos_mask]
        neg_losses = loss_per_sample[neg_mask]
        
        # Always keep all positive samples
        if len(pos_losses) == 0:
            return loss_per_sample.mean()
            
        n_pos = len(pos_losses)
        n_neg_keep = min(int(n_pos * self.config.hard_negative_ratio), len(neg_losses))
        n_neg_keep = max(n_neg_keep, self.config.hard_negative_min_keep)
        n_neg_keep = min(n_neg_keep, len(neg_losses))
        
        if n_neg_keep == 0:
            return pos_losses.mean()
            
        # Select hard negatives based on strategy
        if self.config.hard_negative_strategy == "topk":
            # Select top-k hardest negatives
            _, hard_neg_indices = torch.topk(neg_losses, n_neg_keep)
            selected_neg_losses = neg_losses[hard_neg_indices]
        elif self.config.hard_negative_strategy == "threshold":
            # Select negatives above threshold
            threshold_mask = neg_losses > self.config.hard_negative_threshold
            if threshold_mask.sum() > 0:
                selected_neg_losses = neg_losses[threshold_mask]
            else:
                # Fallback to topk if no samples above threshold
                _, hard_neg_indices = torch.topk(neg_losses, min(n_neg_keep, len(neg_losses)))
                selected_neg_losses = neg_losses[hard_neg_indices]
        else:  # OHEM (Online Hard Example Mining)
            n_keep = int(len(neg_losses) * self.config.ohem_keep_ratio)
            n_keep = max(n_keep, n_neg_keep)
            _, hard_neg_indices = torch.topk(neg_losses, min(n_keep, len(neg_losses)))
            selected_neg_losses = neg_losses[hard_neg_indices]
        
        # Combine positive and selected negative losses
        combined_losses = torch.cat([pos_losses, selected_neg_losses])
        return combined_losses.mean()
    
    def _apply_multiclass_hard_negative_mining(self, loss_per_sample, targets):
        """Apply hard negative mining for multi-class classification."""
        if self.config.hard_negative_strategy == "ohem":
            # Online Hard Example Mining - keep top percentile of hardest examples
            n_keep = int(len(loss_per_sample) * self.config.ohem_keep_ratio)
            n_keep = max(n_keep, self.config.hard_negative_min_keep)
            n_keep = min(n_keep, len(loss_per_sample))
            
            _, hard_indices = torch.topk(loss_per_sample, n_keep)
            return loss_per_sample[hard_indices].mean()
        
        elif self.config.hard_negative_strategy == "threshold":
            # Keep examples above loss threshold
            hard_mask = loss_per_sample > self.config.hard_negative_threshold
            if hard_mask.sum() > 0:
                return loss_per_sample[hard_mask].mean()
            else:
                # Fallback to mean if no hard examples
                return loss_per_sample.mean()
        
        else:  # topk strategy
            # Keep hardest examples per class
            targets_flat = targets.reshape(-1)
            unique_classes = targets_flat.unique()
            
            selected_losses = []
            for class_id in unique_classes:
                if class_id == -1:  # Skip ignore index
                    continue
                    
                class_mask = targets_flat == class_id
                class_losses = loss_per_sample[class_mask]
                
                if len(class_losses) == 0:
                    continue
                    
                # Keep top hard examples for this class
                n_keep = max(1, int(len(class_losses) * 0.5))  # Keep top 50% hardest
                if len(class_losses) > n_keep:
                    _, hard_indices = torch.topk(class_losses, n_keep)
                    selected_losses.append(class_losses[hard_indices])
                else:
                    selected_losses.append(class_losses)
            
            if selected_losses:
                return torch.cat(selected_losses).mean()
            else:
                return loss_per_sample.mean()

    def forward(self, y_prediction, y_truth):
        # 4 multiclasses defined in self.multiClassNums
        # first class is sleepstage and only requires one values for each 30 second epoch

        # lastClassIndex = 0
        losses = []

        # y_prediction = [y_prediction.reshape(-1, y_prediction.shape[-1])]

        # y_truth = y_truth.reshape(-1, y_truth.shape[-1])
        # y_prediction = y_prediction.reshape(-1, y_prediction.shape[-1])
        # y_prediction = (y_prediction[0].reshape(-1, y_prediction[0].shape[-1]), y_prediction[1].reshape(-1, y_prediction[1].shape[-1]))
        # y_prediction = [torch.stack(y_prediction[i]) for i in y_prediction]
        # for yBatchPrediction in y_prediction:

        # cut for reduction
        for labelIndex, (labelPrediction, labelTruth) in enumerate(zip(y_prediction, y_truth)):

            labelPrediction = labelPrediction.reshape(-1, labelPrediction.shape[-1])
            labelTruth = labelTruth.reshape(-1, labelTruth.shape[-1])

            # start = lastClassIndex
            # end = lastClassIndex + classCount
            classCount = self.multiClassNums[labelIndex]

            # if classCount <= 2:
            mask = labelTruth[:, 0] != -1
            labelTruth = labelTruth[mask]
            labelPrediction = labelPrediction[mask]

            if classCount == 1:
                if self.config.hard_negative_mining:
                    loss_per_sample = self.binLoss(labelPrediction.reshape(-1), labelTruth.reshape(-1).float())
                    loss = self.apply_hard_negative_mining(loss_per_sample, labelTruth, classCount)
                else:
                    loss = self.binLoss(labelPrediction.reshape(-1), labelTruth.reshape(-1).float())
            elif classCount == 2:
                if self.config.hard_negative_mining:
                    loss_per_sample = self.binLoss(labelPrediction, labelTruth)
                    loss = self.apply_hard_negative_mining(loss_per_sample, labelTruth, classCount)
                else:
                    loss = self.binLoss(labelPrediction, labelTruth)
            else:
                pred = labelPrediction
                if self.normalize is not None:
                    pred = self.normalize(pred)
                
                if self.config.hard_negative_mining:
                    loss_per_sample = nn.CrossEntropyLoss(ignore_index=-1, reduction='none')(pred, labelTruth.reshape(-1).long())
                    loss = self.apply_hard_negative_mining(loss_per_sample, labelTruth, classCount)
                else:
                    loss = nn.CrossEntropyLoss(ignore_index=-1)(pred, labelTruth.reshape(-1).long())
            # lastClassIndex = end
            losses.append(loss)


        self.stats = {f"task{taskIndex}_loss": l.cpu().detach().item() for taskIndex, l in enumerate(losses)}
        if self.reduce == "same":
            biggestLost = max(losses)
            weights = [biggestLost/l for l in losses]
            losses = [l*w for l,w in zip(weights, losses)]
            loss = torch.stack(losses).sum()
        elif self.reduce == "uncertainty":
            loss, uncertainty_weights = self.uncertainty_weight(torch.stack(losses))
            # print("uncertainty_weights:")
            # print([w.cpu().detach().item() for w in uncertainty_weights])
        elif self.reduce == "gradnorm":
            gradnorm_loss, grad_weights = self.gradnorm(losses, self.shared_params)
            loss = (grad_weights * torch.stack(losses)).sum() + gradnorm_loss
        elif self.reduce == "uncertainty_gradnorm":
            gradnorm_loss, grad_weights = self.gradnorm(losses, self.shared_params)
            uncertainty_loss, uncertainty_weights = self.uncertainty_weight(torch.stack(losses))
            loss = (grad_weights * torch.stack(losses)).sum() + gradnorm_loss
            loss = loss + uncertainty_loss
            # print("uncertainty_weights:")
            # print([w.cpu().detach().item() for w in uncertainty_weights])
        else:
            loss = torch.stack(losses).mean() if self.reduce == "mean" else torch.stack(losses).sum()

        return loss

class UncertaintyWeightLoss(nn.Module):
    def __init__(self, n_tasks=12):
        super(UncertaintyWeightLoss, self).__init__()
        # self.log_vars = nn.Parameter(-torch.log(torch.tensor([3.962090492248535, 18.56128692626953, 2.7362115383148193])))
        self.log_vars = nn.Parameter(torch.zeros(n_tasks))

    def forward(self, task_losses):
        weights = torch.exp(-self.log_vars)
        weighted_losses = weights * task_losses + self.log_vars
        return weighted_losses.mean(), weights

class GradNorm(nn.Module):
    def __init__(self, n_tasks=12, alpha=1.5):
        super(GradNorm, self).__init__()
        self.alpha = alpha
        self.weights = nn.Parameter(torch.ones(n_tasks))
        self.initial_losses = None  # Track initial losses

    def forward(self, task_losses, shared_parameters):
        # Get initial loss values for each task
        if self.initial_losses is None:
            self.initial_losses = [loss.detach().clone() for loss in task_losses]
        avg_initial_loss = torch.stack(self.initial_losses).mean()

        # Calculate relative inverse training rate r(t)
        loss_ratios = torch.stack([loss / init_loss for loss, init_loss
                                 in zip(task_losses, self.initial_losses)])


        inverse_train_rates = loss_ratios / loss_ratios.mean()

        # Calculate gradients for shared parameters
        task_gradients = []
        for loss in task_losses:
            grads = torch.autograd.grad(loss, shared_parameters, retain_graph=True, allow_unused=True)
            total_norm = torch.sqrt(sum(grad.norm(2) ** 2 for grad in grads if grad is not None))
            task_gradients.append(total_norm)

        task_gradients = torch.stack(task_gradients)

        # Calculate target gradients
        target_gradients = (inverse_train_rates ** self.alpha) * task_gradients.mean()

        # Calculate gradnorm loss
        gradnorm_loss = torch.sum(torch.abs(task_gradients - target_gradients))


        return gradnorm_loss, self.weights


class TemporalBalanceCrossEntropyLoss(nn.Module):
    """
    A custom loss function that applies class weighting dynamically based on
    the temporal distribution of classes in each batch.

    This is particularly useful for time-series classification tasks where
    class imbalance can vary across different batches and time windows.
    """

    def __init__(
        self,
        num_classes,
        weight=None,
        reduction="mean",
        ignore_index=-100,
        weight_normalization=True,
        epsilon=1e-6,
    ):
        """
        Initialize the TemporalBalanceCrossEntropyLoss.

        Args:
            num_classes (int): Number of classes in the classification task
            weight (torch.Tensor, optional): Manual weight tensor to apply to classes
            reduction (str): Specifies the reduction to apply to the output ('none', 'mean', 'sum')
            ignore_index (int): Specifies a target value that is ignored
            weight_normalization (bool): Whether to normalize weights to sum to num_classes
            epsilon (float): Small value to avoid division by zero
        """
        super(TemporalBalanceCrossEntropyLoss, self).__init__()
        self.num_classes = num_classes
        self.manual_weight = weight
        self.reduction = reduction
        self.ignore_index = ignore_index
        self.weight_normalization = weight_normalization
        self.epsilon = epsilon

    def calculate_batch_weights(self, targets):
        """
        Calculate class weights based on the frequency of each class in the current batch.

        Args:
            targets: The target tensor

        Returns:
            A tensor of weights for each class
        """
        # Flatten the targets to count all instances
        if targets.dim() > 1:
            flat_targets = targets.reshape(-1)
        else:
            flat_targets = targets

        # Count occurrences of each class
        class_counts = torch.zeros(self.num_classes, device=targets.device)
        for i in range(self.num_classes):
            class_counts[i] = (flat_targets == i).sum().float()

        # Add a small epsilon to avoid division by zero
        class_counts = class_counts + self.epsilon

        # Calculate weights as inverse of frequency
        # More frequent classes get lower weights
        weights = 1.0 / class_counts

        # Normalize weights to sum to num_classes if specified
        if self.weight_normalization:
            weights = weights * (self.num_classes / weights.sum())

        return weights

    def forward(self, inputs, targets):
        """
        Forward pass for the loss calculation.

        Args:
            inputs (torch.Tensor): Predicted class scores (B, C, T) or (B, C)
                where B is batch size, C is number of classes, T is sequence length
            targets (torch.Tensor): Ground truth class indices (B, T) or (B)

        Returns:
            torch.Tensor: Computed loss value
        """
        # Calculate dynamic weights for this batch if no manual weights provided
        if self.manual_weight is None:
            weights = self.calculate_batch_weights(targets)
        else:
            weights = self.manual_weight

        # inputs = inputs.reshape(-1, self.num_classes)
        # targets = targets.reshape(-1)

        # Apply cross entropy with the calculated weights
        loss = F.cross_entropy(
            inputs,
            targets,
            weight=weights,
            ignore_index=self.ignore_index,
            reduction=self.reduction,
        )

        return loss


class FocalTemporalBalanceCrossEntropyLoss(nn.Module):
    def __init__(self, num_classes, gamma=2.0, alpha=None):
        super().__init__()
        self.gamma = gamma
        self.alpha = alpha
        self.num_classes = num_classes

    def forward(self, inputs, targets):
        ce_loss = F.cross_entropy(inputs, targets, reduction="none", ignore_index=-1)
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss

        if self.alpha is not None:
            focal_loss = self.alpha[targets] * focal_loss

        return focal_loss.mean()


class MultiTaskLoss(nn.Module):
    """
    A loss function for multi-task learning that combines losses from different tasks.
    """

    def __init__(self, task_losses, task_weights=None):
        """
        Initialize the MultiTaskLoss.

        Args:
            task_losses (dict): Dictionary mapping task names to loss functions
            task_weights (dict, optional): Dictionary mapping task names to loss weights
        """
        super(MultiTaskLoss, self).__init__()
        self.task_losses = task_losses
        self.task_weights = task_weights or {task: 1.0 for task in task_losses}

    def forward(self, outputs, targets):
        """
        Forward pass for the multi-task loss calculation.

        Args:
            outputs (dict): Dictionary mapping task names to model outputs
            targets (dict): Dictionary mapping task names to target values

        Returns:
            torch.Tensor: Weighted sum of task losses
        """
        total_loss = 0.0
        losses = {}

        for task_name, loss_fn in self.task_losses.items():
            if task_name in outputs and task_name in targets:
                task_loss = loss_fn(outputs[task_name], targets[task_name])
                weighted_loss = self.task_weights[task_name] * task_loss
                total_loss += weighted_loss
                losses[task_name] = task_loss.item()

        return total_loss, losses

# Add a focal loss implementation for binary classification
class FocalBCEWithLogitsLoss(nn.Module):
    def __init__(self, gamma=2.0, alpha=0.25):
        super().__init__()
        self.gamma = gamma
        self.alpha = alpha

    def forward(self, y_pred, y_true):
        y_pred = y_pred.reshape(-1)
        y_true = y_true.view(-1)

        # Filter out ignored indices
        mask = y_true > -0.5
        y_pred = y_pred[mask]
        y_true = y_true[mask]

        # BCE loss
        bce_loss = F.binary_cross_entropy_with_logits(y_pred, y_true.float(), reduction='none')

        # Focal loss
        pt = torch.exp(-bce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * bce_loss

        return focal_loss.mean()


class DiceLoss(nn.Module):
    """
    Dice Loss for segmentation tasks.

    The Dice coefficient is a measure of overlap between two samples and can be used to compare
    the pixel-wise agreement between a predicted segmentation and its corresponding ground truth.
    This loss is useful for handling imbalanced segmentation problems.
    """
    def __init__(self, smooth=1.0, reduction='mean', ignore_index=-1, multi_class=False):
        """
        Initialize the DiceLoss.

        Args:
            smooth (float): Small constant added to numerator and denominator to avoid division by zero
            reduction (str): Specifies the reduction to apply to the output ('none', 'mean', 'sum')
            ignore_index (int): Specifies a target value that is ignored
            multi_class (bool): Whether to use multi-class Dice Loss
        """
        super(DiceLoss, self).__init__()
        self.smooth = smooth
        self.reduction = reduction
        self.ignore_index = ignore_index
        self.multi_class = multi_class

    def forward(self, y_pred, y_true):
        """
        Forward pass for the Dice Loss calculation.

        Args:
            y_pred (torch.Tensor): Predicted probabilities after sigmoid/softmax
                                   For multi-class, should be shape (B, C, *) where C is number of classes
            y_true (torch.Tensor): Ground truth labels
                                   For multi-class, should be shape (B, *) with class indices

        Returns:
            torch.Tensor: Computed Dice Loss
        """
        if self.multi_class:
            return self._multi_class_dice_loss(y_pred, y_true)
        else:
            return self._binary_dice_loss(y_pred, y_true)

    def _binary_dice_loss(self, y_pred, y_true):
        """
        Binary Dice Loss calculation.
        """
        # Flatten the predictions and targets
        y_pred = y_pred.reshape(-1)
        y_true = y_true.reshape(-1)

        # Filter out ignored indices
        if self.ignore_index != -1:
            mask = y_true != self.ignore_index
            y_pred = y_pred[mask]
            y_true = y_true[mask]

        # Convert targets to float for calculation
        y_true = y_true.float()

        # Calculate intersection and union
        intersection = (y_pred * y_true).sum()
        union = y_pred.sum() + y_true.sum()

        # Calculate Dice coefficient
        dice = (2. * intersection + self.smooth) / (union + self.smooth)

        # Return Dice Loss (1 - Dice coefficient)
        loss = 1 - dice

        return loss

    def _multi_class_dice_loss(self, y_pred, y_true):
        """
        Multi-class Dice Loss calculation.

        For multi-class segmentation, we calculate the Dice Loss for each class
        and then average over all classes.
        """
        # Get the number of classes
        num_classes = y_pred.shape[1]

        # Apply softmax to predictions if not already done
        if y_pred.dim() > 2:
            # For multi-dimensional input (B, C, *)
            y_pred = F.softmax(y_pred, dim=1)

        # One-hot encode the target
        y_true_one_hot = F.one_hot(y_true.long(), num_classes).permute(0, 3, 1, 2).float()

        # Initialize total loss
        total_loss = 0.0

        # Calculate Dice Loss for each class
        for i in range(num_classes):
            # Extract predictions and targets for current class
            pred_class = y_pred[:, i, ...].reshape(-1)
            true_class = y_true_one_hot[:, i, ...].reshape(-1)

            # Filter out ignored indices
            if self.ignore_index != -1:
                mask = y_true.reshape(-1) != self.ignore_index
                pred_class = pred_class[mask]
                true_class = true_class[mask]

            # Calculate intersection and union
            intersection = (pred_class * true_class).sum()
            union = pred_class.sum() + true_class.sum()

            # Calculate Dice coefficient for this class
            dice = (2. * intersection + self.smooth) / (union + self.smooth)

            # Add to total loss
            total_loss += (1 - dice)

        # Average over all classes
        if self.reduction == 'mean':
            return total_loss / num_classes
        elif self.reduction == 'sum':
            return total_loss
        else:
            return total_loss
