import torch
import torch.nn as nn
from collections import OrderedDict
import numpy as np
from torchviz import make_dot
import matplotlib.pyplot as plt

class ModelAnalyzer:
    def __init__(self, model, input_shape):
        self.model = model
        self.input_shape = input_shape
        
    def analyze_architecture(self):
        """Comprehensive architecture analysis without torchsummary"""
        print("=== MODEL ARCHITECTURE ANALYSIS ===")
        
        # 1. Basic model info
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        print(f"Total parameters: {total_params:,}")
        print(f"Trainable parameters: {trainable_params:,}")
        print(f"Model size (MB): {total_params * 4 / 1024 / 1024:.2f}")
        
        # 2. Layer-wise parameter analysis
        self._analyze_layer_parameters()
        
        # 3. Model structure
        self._print_model_structure()
        
        # 4. Forward pass analysis
        self._analyze_forward_pass()
    
    def _analyze_layer_parameters(self):
        """Analyze parameters per layer without recursion"""
        print("\n=== PARAMETER COUNT PER MODULE ===")
        
        param_dict = {}
        for name, param in self.model.named_parameters():
            # Extract module name (everything before the last dot)
            module_name = '.'.join(name.split('.')[:-1]) if '.' in name else name
            
            if module_name not in param_dict:
                param_dict[module_name] = 0
            param_dict[module_name] += param.numel()
        
        # Sort by parameter count
        sorted_params = sorted(param_dict.items(), key=lambda x: x[1], reverse=True)
        
        for name, count in sorted_params:
            print(f"{name}: {count:,} parameters")
    
    def _print_model_structure(self):
        """Print model structure safely"""
        print("\n=== MODEL STRUCTURE ===")
        
        def print_module(module, prefix="", max_depth=3, current_depth=0):
            if current_depth >= max_depth:
                return
                
            for name, child in module.named_children():
                child_name = f"{prefix}.{name}" if prefix else name
                print(f"{'  ' * current_depth}{child_name}: {child.__class__.__name__}")
                
                # Print some key parameters if available
                if hasattr(child, 'in_channels') and hasattr(child, 'out_channels'):
                    print(f"{'  ' * (current_depth + 1)}├─ Channels: {child.in_channels} → {child.out_channels}")
                elif hasattr(child, 'in_features') and hasattr(child, 'out_features'):
                    print(f"{'  ' * (current_depth + 1)}├─ Features: {child.in_features} → {child.out_features}")
                
                # Recurse into children
                if len(list(child.children())) > 0:
                    print_module(child, child_name, max_depth, current_depth + 1)
        
        print_module(self.model)
    
    def _analyze_forward_pass(self):
        """Analyze forward pass with shape tracking"""
        print("\n=== FORWARD PASS ANALYSIS ===")
        
        # Create a dummy input
        if len(self.input_shape) == 2:  # (length, channels)
            dummy_input = torch.randn(1, self.input_shape[1], self.input_shape[0])
        else:  # assume (batch, channels, length)
            dummy_input = torch.randn(1, *self.input_shape)
        
        # Track shapes through the network
        shapes = self._track_tensor_shapes(dummy_input)
        
        for layer_name, shape_info in shapes.items():
            print(f"{layer_name}: {shape_info['input_shape']} → {shape_info['output_shape']}")
    
    def _track_tensor_shapes(self, input_tensor):
        """Track tensor shapes through forward pass"""
        shapes = OrderedDict()
        
        def hook_fn(name):
            def hook(module, input, output):
                if isinstance(input, (tuple, list)) and len(input) > 0:
                    input_shape = input[0].shape
                else:
                    input_shape = input.shape if hasattr(input, 'shape') else 'Unknown'
                
                if isinstance(output, (tuple, list)):
                    if len(output) > 0 and hasattr(output[0], 'shape'):
                        output_shape = [o.shape for o in output if hasattr(o, 'shape')]
                    else:
                        output_shape = 'Multiple outputs'
                else:
                    output_shape = output.shape if hasattr(output, 'shape') else 'Unknown'
                
                shapes[name] = {
                    'input_shape': input_shape,
                    'output_shape': output_shape
                }
            return hook
        
        # Register hooks for key layers only
        hooks = []
        key_layer_types = (nn.Conv1d, nn.Linear, nn.LSTM, nn.GRU, nn.MaxPool1d, nn.AdaptiveAvgPool1d)
        
        for name, module in self.model.named_modules():
            if isinstance(module, key_layer_types):
                hooks.append(module.register_forward_hook(hook_fn(name)))
        
        # Forward pass
        try:
            with torch.no_grad():
                self.model.eval()
                _ = self.model(input_tensor)
        except Exception as e:
            print(f"Forward pass failed: {e}")
        finally:
            # Remove hooks
            for hook in hooks:
                hook.remove()
        
        return shapes
    
    def create_simple_summary(self, input_tensor=None):
        """Create a simple model summary without external dependencies"""
        if input_tensor is None:
            if len(self.input_shape) == 2:
                input_tensor = torch.randn(1, self.input_shape[1], self.input_shape[0])
            else:
                input_tensor = torch.randn(1, *self.input_shape)
        
        print("\n=== SIMPLE MODEL SUMMARY ===")
        print(f"Input shape: {input_tensor.shape}")
        
        # Get output shape
        try:
            self.model.eval()
            with torch.no_grad():
                output = self.model(input_tensor)
                if isinstance(output, (list, tuple)):
                    print(f"Output shapes: {[o.shape for o in output]}")
                else:
                    print(f"Output shape: {output.shape}")
        except Exception as e:
            print(f"Could not determine output shape: {e}")
        
        # Memory usage estimation
        total_params = sum(p.numel() for p in self.model.parameters())
        param_size = total_params * 4  # 4 bytes per float32
        
        # Estimate activation memory (rough approximation)
        input_size = np.prod(input_tensor.shape) * 4
        estimated_activation_size = input_size * 10  # rough multiplier
        
        print(f"\nMemory Usage Estimation:")
        print(f"Parameters: {param_size / 1024 / 1024:.2f} MB")
        print(f"Estimated activations: {estimated_activation_size / 1024 / 1024:.2f} MB")
        print(f"Total estimated: {(param_size + estimated_activation_size) / 1024 / 1024:.2f} MB")
    
    def visualize_computation_graph(self, sample_input=None, save_path=None):
        """Create computation graph visualization"""
        if sample_input is None:
            if len(self.input_shape) == 2:
                sample_input = torch.randn(1, self.input_shape[1], self.input_shape[0])
            else:
                sample_input = torch.randn(1, *self.input_shape)
        
        try:
            self.model.eval()
            output = self.model(sample_input)
            
            # For multi-output models, combine outputs
            if isinstance(output, list):
                # Create a scalar output for visualization
                combined_output = sum(o.mean() for o in output)
                dot = make_dot(combined_output, params=dict(self.model.named_parameters()))
            else:
                dot = make_dot(output.mean(), params=dict(self.model.named_parameters()))
            
            if save_path:
                dot.render(save_path, format='png', cleanup=True)
                print(f"Computation graph saved to {save_path}.png")
            
            return dot
            
        except Exception as e:
            print(f"Could not create computation graph: {e}")
            return None
    
    def analyze_model_complexity(self):
        """Analyze model complexity metrics"""
        print("\n=== MODEL COMPLEXITY ANALYSIS ===")
        
        # Count different layer types
        layer_counts = {}
        for name, module in self.model.named_modules():
            layer_type = module.__class__.__name__
            layer_counts[layer_type] = layer_counts.get(layer_type, 0) + 1
        
        print("Layer type distribution:")
        for layer_type, count in sorted(layer_counts.items()):
            if count > 1:  # Only show layers that appear multiple times
                print(f"  {layer_type}: {count}")
        
        # Analyze depth
        max_depth = 0
        def get_depth(module, current_depth=0):
            nonlocal max_depth
            max_depth = max(max_depth, current_depth)
            for child in module.children():
                get_depth(child, current_depth + 1)
        
        get_depth(self.model)
        print(f"Maximum depth: {max_depth}")
        
        # Analyze parameter distribution
        param_sizes = [p.numel() for p in self.model.parameters()]
        if param_sizes:
            print(f"Parameter statistics:")
            print(f"  Largest layer: {max(param_sizes):,} parameters")
            print(f"  Smallest layer: {min(param_sizes):,} parameters")
            print(f"  Average layer size: {np.mean(param_sizes):,.0f} parameters")
            print(f"  Median layer size: {np.median(param_sizes):,.0f} parameters")