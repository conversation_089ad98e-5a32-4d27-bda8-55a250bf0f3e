import torch
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import numpy as np

class FeatureAnalyzer:
    def __init__(self, model):
        self.model = model
        self.features = {}
        
    def extract_intermediate_features(self, dataloader, device, layer_names):
        """Extract features from intermediate layers"""
        
        def hook_fn(name):
            def hook(module, input, output):
                if isinstance(output, torch.Tensor):
                    self.features[name] = output.detach().cpu()
            return hook
        
        # Register hooks
        hooks = []
        for name, module in self.model.named_modules():
            if name in layer_names:
                hooks.append(module.register_forward_hook(hook_fn(name)))
        
        self.model.eval()
        all_features = {name: [] for name in layer_names}
        
        with torch.no_grad():
            for batch_x, _ in dataloader:
                batch_x = batch_x.to(device)
                _ = self.model(batch_x)
                
                # Collect features
                for name in layer_names:
                    if name in self.features:
                        all_features[name].append(self.features[name])
        
        # Remove hooks
        for hook in hooks:
            hook.remove()
        
        # Concatenate features
        for name in layer_names:
            if all_features[name]:
                all_features[name] = torch.cat(all_features[name], dim=0)
        
        return all_features
    
    def visualize_feature_spaces(self, features, labels, method='tsne'):
        """Visualize high-dimensional features in 2D"""
        fig, axes = plt.subplots(1, len(features), figsize=(5*len(features), 5))
        if len(features) == 1:
            axes = [axes]
        
        for i, (layer_name, feature_tensor) in enumerate(features.items()):
            # Flatten features for visualization
            feature_flat = feature_tensor.view(feature_tensor.size(0), -1).numpy()
            
            if method == 'pca':
                reducer = PCA(n_components=2)
            else:  # tsne
                reducer = TSNE(n_components=2, random_state=42)
            
            features_2d = reducer.fit_transform(feature_flat)
            
            scatter = axes[i].scatter(features_2d[:, 0], features_2d[:, 1], 
                                    c=labels, cmap='tab10', alpha=0.6)
            axes[i].set_title(f'{layer_name} Features ({method.upper()})')
            plt.colorbar(scatter, ax=axes[i])
        
        plt.tight_layout()
        fig.savefig("feature_spaces")
        return fig