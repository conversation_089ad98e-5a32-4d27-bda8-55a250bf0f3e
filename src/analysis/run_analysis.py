from matplotlib import pyplot as plt
import numpy as np
import torch
from .attention_analyzer import AttentionAnalyzer
from .feature_analyzer import FeatureAnalyzer
from .loss_analyzer import LossAnalyzer
from .model_inspector import ModelAnalyzer
from .task_analyzer import TaskAnalyzer
import seaborn as sns


def analyze_model(model_path, data_loader, device):
    # Load model

    model = torch.load("/workspace/pyphases-main/spp-sleepnet/results/TSM-3edb8857-f98e6a85-4ff76fed-3edb8857-SleepPPGNet-7d27eacf-7f1c42f1-8a35a439-88781e13-0/checkpointModel_77_0.711_0.791_0.543_0.380_0.367_609.000_0.629_0.604_0.827_0.727_0.714_270.000.pkl", map_location=device)
    model.eval()
    
    # Initialize analyzers
    model_analyzer = ModelAnalyzer(model, input_shape=(1, 30000))  # Adjust input shape
    task_analyzer = TaskAnalyzer(model, ['Sleep', 'Arousal', 'Apnea', 'LM'])
    loss_analyzer = LossAnalyzer(model, model.lossCriterium)
    feature_analyzer = FeatureAnalyzer(model)
    attention_analyzer = AttentionAnalyzer(model)
    
    # Run analyses
    print("1. Architecture Analysis")
    model_analyzer.analyze_architecture()
    
    print("2. Task Performance Analysis")
    predictions, targets = task_analyzer.analyze_task_predictions(data_loader, device)
    task_analyzer.plot_task_confusion_matrices(predictions, targets)
    
    print("3. Loss Analysis")
    task_losses = loss_analyzer.analyze_task_losses(data_loader, device)
    print(f"Individual task losses: {task_losses}")
    
    print("4. Feature Analysis")
    layer_names = ['fe_layers.0', 'fe_layers.4', 'sleep_tcn1', 'micro_tcn1']
    features = feature_analyzer.extract_intermediate_features(data_loader, device, layer_names)
    
    # Visualize feature spaces for different tasks
    sample_labels = targets[0][:1000].flatten().numpy()  # Use sleep stage labels
    sample_features = {name: feat[:1000] for name, feat in features.items()}
    feature_analyzer.visualize_feature_spaces(sample_features, sample_labels)
    
    print("5. Attention/Importance Analysis")
    for task_idx, task_name in enumerate(['Sleep', 'Arousal', 'Apnea', 'LM']):
        importance = attention_analyzer.analyze_temporal_patterns(data_loader, device, task_idx)
        fig = attention_analyzer.plot_temporal_importance(importance, sampling_rate=100)
        fig.suptitle(f'Temporal Importance for {task_name} Task')
        plt.savefig(f'importance_{task_name.lower()}.png')
        plt.close()
    
    print("6. Multi-task Learning Analysis")
    analyze_multitask_learning(model, data_loader, device)

def analyze_multitask_learning(model, data_loader, device):
    """Specific analysis for multi-task learning aspects"""
    
    # Task similarity analysis
    task_similarities = compute_task_similarities(model, data_loader, device)
    plot_task_similarity_matrix(task_similarities)
    
    # Shared vs task-specific feature analysis
    analyze_feature_sharing(model, data_loader, device)
    
    # Task interference analysis
    analyze_task_interference(model, data_loader, device)

def compute_task_similarities(model, data_loader, device):
    """Compute similarities between task representations"""
    model.eval()
    
    # Extract features before task-specific heads
    shared_features = []
    task_features = [[] for _ in range(len(model.multiClassNums))]
    
    def hook_shared(module, input, output):
        shared_features.append(output.detach().cpu())
    
    def hook_task(task_idx):
        def hook(module, input, output):
            task_features[task_idx].append(output.detach().cpu())
        return hook
    
    # Register hooks
    hooks = []
    # Hook to shared features (before task-specific processing)
    if hasattr(model.model, 'fe_layers'):
        hooks.append(model.model.fe_layers[-1].register_forward_hook(hook_shared))
    
    # Hook to task-specific features
    for i, fc in enumerate(model.model.fcs):
        hooks.append(fc.register_forward_hook(hook_task(i)))
    
    with torch.no_grad():
        for batch_x, _ in data_loader:
            batch_x = batch_x.to(device)
            _ = model(batch_x)
    
    # Remove hooks
    for hook in hooks:
        hook.remove()
    
    # Compute similarities
    similarities = np.zeros((len(model.multiClassNums), len(model.multiClassNums)))
    
    for i in range(len(model.multiClassNums)):
        for j in range(len(model.multiClassNums)):
            if i != j and task_features[i] and task_features[j]:
                feat_i = torch.cat(task_features[i], dim=0).flatten()
                feat_j = torch.cat(task_features[j], dim=0).flatten()
                
                # Compute cosine similarity
                similarity = torch.cosine_similarity(feat_i.unsqueeze(0), feat_j.unsqueeze(0))
                similarities[i, j] = similarity.item()
    
    return similarities

def plot_task_similarity_matrix(similarities):
    """Plot task similarity matrix"""
    task_names = ['Sleep', 'Arousal', 'Apnea', 'LM']
    
    plt.figure(figsize=(8, 6))
    sns.heatmap(similarities, annot=True, cmap='coolwarm', center=0,
                xticklabels=task_names, yticklabels=task_names)
    plt.title('Task Similarity Matrix')
    plt.tight_layout()
    plt.savefig('task_similarity_matrix.png')
    plt.show()

def analyze_feature_sharing(model, data_loader, device):
    """Analyze how much features are shared vs task-specific"""
    
    # Get activations from different parts of the network
    activations = {}
    
    def get_activation(name):
        def hook(model, input, output):
            activations[name] = output.detach()
        return hook
    
    hooks = []
    # Shared feature extractor
    hooks.append(model.model.fe_layers[-1].register_forward_hook(get_activation('shared_features')))
    
    # Task-specific features
    if hasattr(model.model, 'sleep_tcn2'):
        hooks.append(model.model.sleep_tcn2.register_forward_hook(get_activation('sleep_features')))
    if hasattr(model.model, 'micro_tcn2'):
        hooks.append(model.model.micro_tcn2.register_forward_hook(get_activation('micro_features')))
    
    model.eval()
    with torch.no_grad():
        for batch_x, _ in data_loader:
            batch_x = batch_x.to(device)
            _ = model(batch_x)
            break  # Just one batch for analysis
    
    # Remove hooks
    for hook in hooks:
        hook.remove()
    
    # Analyze feature statistics
    print("\n=== FEATURE SHARING ANALYSIS ===")
    for name, activation in activations.items():
        print(f"{name}:")
        print(f"  Shape: {activation.shape}")
        print(f"  Mean activation: {activation.mean().item():.4f}")
        print(f"  Std activation: {activation.std().item():.4f}")
        print(f"  Sparsity (% zeros): {(activation == 0).float().mean().item()*100:.2f}%")

def analyze_task_interference(model, data_loader, device):
    """Analyze potential negative transfer between tasks"""
    
    print("\n=== TASK INTERFERENCE ANALYSIS ===")
    
    # Single-task performance simulation
    model.eval()
    task_performances = {}
    
    with torch.no_grad():
        all_predictions = []
        all_targets = []
        
        for batch_x, batch_y in data_loader:
            batch_x = batch_x.to(device)
            batch_y = [y.to(device) for y in batch_y]
            
            predictions = model(batch_x)
            all_predictions.append(predictions)
            all_targets.append(batch_y)
    
    # Analyze gradient conflicts
    analyze_gradient_conflicts(model, data_loader, device)

def analyze_gradient_conflicts(model, data_loader, device):
    """Analyze gradient conflicts between tasks"""
    
    model.train()
    
    # Get one batch
    for batch_x, batch_y in data_loader:
        batch_x = batch_x.to(device)
        batch_y = [y.to(device) for y in batch_y]
        break
    
    # Compute gradients for each task separately
    task_gradients = []
    
    for task_idx in range(len(model.multiClassNums)):
        model.zero_grad()
        
        predictions = model(batch_x)
        
        # Create a loss for just this task
        task_loss = model.lossCriterium.loss(predictions[task_idx], batch_y[task_idx])
        task_loss.backward(retain_graph=True)
        
        # Collect gradients
        grads = []
        for param in model.parameters():
            if param.grad is not None:
                grads.append(param.grad.clone().flatten())
        
        if grads:
            task_gradients.append(torch.cat(grads))
    
    # Compute gradient similarities
    print("\n=== GRADIENT CONFLICT ANALYSIS ===")
    task_names = ['Sleep', 'Arousal', 'Apnea', 'LM']
    
    for i in range(len(task_gradients)):
        for j in range(i+1, len(task_gradients)):
            if i < len(task_names) and j < len(task_names):
                cosine_sim = torch.cosine_similarity(
                    task_gradients[i].unsqueeze(0), 
                    task_gradients[j].unsqueeze(0)
                ).item()
                
                print(f"Gradient similarity {task_names[i]} vs {task_names[j]}: {cosine_sim:.4f}")
                
                if cosine_sim < 0:
                    print(f"  ⚠️  Potential negative transfer detected!")

# Additional analysis functions
def analyze_model_robustness(model, data_loader, device):
    """Analyze model robustness to input perturbations"""
    
    print("\n=== ROBUSTNESS ANALYSIS ===")
    
    model.eval()
    noise_levels = [0.01, 0.05, 0.1, 0.2]
    original_performance = []
    noisy_performance = {level: [] for level in noise_levels}
    
    with torch.no_grad():
        for batch_x, batch_y in data_loader:
            batch_x = batch_x.to(device)
            
            # Original predictions
            orig_pred = model(batch_x)
            original_performance.append(orig_pred)
            
            # Noisy predictions
            for noise_level in noise_levels:
                noise = torch.randn_like(batch_x) * noise_level
                noisy_x = batch_x + noise
                noisy_pred = model(noisy_x)
                noisy_performance[noise_level].append(noisy_pred)
    
    # Compute performance degradation
    for noise_level in noise_levels:
        # This would need task-specific metrics
        print(f"Noise level {noise_level}: Performance analysis needed")

def analyze_temporal_dependencies(model, data_loader, device):
    """Analyze what temporal dependencies the model learns"""
    
    print("\n=== TEMPORAL DEPENDENCY ANALYSIS ===")
    
    # Analyze TCN receptive fields
    def compute_receptive_field(dilation_rates, kernel_size):
        receptive_field = 1
        for dilation in dilation_rates:
            receptive_field += (kernel_size - 1) * dilation
        return receptive_field
    
    sleep_rf = compute_receptive_field([1, 2, 4, 8, 16, 32], 7)
    event_rf = compute_receptive_field([1, 2, 4, 8], 7)
    
    print(f"Sleep TCN receptive field: {sleep_rf} time steps")
    print(f"Event TCN receptive field: {event_rf} time steps")
    
    # Convert to seconds (assuming 100 Hz sampling)
    print(f"Sleep TCN temporal span: {sleep_rf/100:.2f} seconds")
    print(f"Event TCN temporal span: {event_rf/100:.2f} seconds")

if __name__ == "__main__":
    # Example usage
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Load your model and data
    model_path = "path/to/your/trained/model.pth"
    # data_loader = your_data_loader
    
    # Run comprehensive analysis
    # analyze_model(model_path, data_loader, device)
    
    print("Analysis complete! Check generated plots and logs.")
