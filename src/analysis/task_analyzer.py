import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report
from sklearn.manifold import TSNE
import torch

class TaskAnalyzer:
    def __init__(self, model, task_names):
        self.model = model
        self.task_names = task_names
        
    def analyze_task_predictions(self, dataloader, device):
        """Analyze predictions for each task"""
        # self.model.eval()
        all_predictions = [[] for _ in self.task_names]
        all_targets = [[] for _ in self.task_names]
        all_features = []
        
        with torch.no_grad():
            for batch_x, batch_y in dataloader:
                batch_x = batch_x.to(device)
                
                # Get predictions and intermediate features
                predictions = self.model(batch_x)
                
                # Store predictions and targets for each task
                for i, (pred, target) in enumerate(zip(predictions, batch_y)):
                    all_predictions[i].append(pred.cpu())
                    all_targets[i].append(target.cpu())
        
        # Concatenate results
        for i in range(len(self.task_names)):
            all_predictions[i] = torch.cat(all_predictions[i], dim=0)
            all_targets[i] = torch.cat(all_targets[i], dim=0)
            
        return all_predictions, all_targets
    
    def plot_task_confusion_matrices(self, predictions, targets):
        """Plot confusion matrices for each classification task"""
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        axes = axes.flatten()
        
        for i, task_name in enumerate(self.task_names):
            if i >= len(axes):
                break
                
            pred = predictions[i]
            target = targets[i]
            
            # Convert to class predictions
            if pred.shape[-1] > 1:  # Multi-class
                pred_classes = torch.argmax(pred, dim=-1).flatten()
                target_classes = target.flatten()
            else:  # Binary
                pred_classes = (torch.sigmoid(pred) > 0.5).flatten()
                target_classes = target.flatten()
            
            # Remove ignored indices
            mask = target_classes != -1
            pred_classes = pred_classes[mask]
            target_classes = target_classes[mask]
            
            # Create confusion matrix
            cm = confusion_matrix(target_classes.numpy(), pred_classes.numpy())
            
            sns.heatmap(cm, annot=True, fmt='d', ax=axes[i], cmap='Blues')
            axes[i].set_title(f'{task_name} Confusion Matrix')
            axes[i].set_xlabel('Predicted')
            axes[i].set_ylabel('Actual')
        
        plt.tight_layout()
        fig.savefig("task_confusion.svg")
        return fig
    
    def analyze_class_distributions(self, targets):
        """Analyze class distributions for each task"""
        fig, axes = plt.subplots(2, 3, figsize=(15, 8))
        axes = axes.flatten()
        
        for i, (task_name, target) in enumerate(zip(self.task_names, targets)):
            if i >= len(axes):
                break
                
            target_flat = target.flatten()
            # Remove ignored indices
            target_flat = target_flat[target_flat != -1]
            
            unique, counts = torch.unique(target_flat, return_counts=True)
            
            axes[i].bar(unique.numpy(), counts.numpy())
            axes[i].set_title(f'{task_name} Class Distribution')
            axes[i].set_xlabel('Class')
            axes[i].set_ylabel('Count')
        
        plt.tight_layout()
        fig.savefig("class_distribution.svg")
        return fig