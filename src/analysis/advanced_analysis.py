from captum.attr import IntegratedGradients, GradientShap, Occlusion
from captum.attr import visualization as viz
import torch

class AdvancedModelAnalyzer:
    def __init__(self, model, device):
        self.model = model
        self.device = device
        
    def integrated_gradients_analysis(self, input_data, target_task=0, target_class=None):
        """Use Captum's Integrated Gradients for feature attribution"""
        
        def model_wrapper(x):
            outputs = self.model(x)
            if isinstance(outputs, list):
                return outputs[target_task]
            return outputs
        
        ig = IntegratedGradients(model_wrapper)
        
        # Option 1: Specify target class/index
        if target_class is not None:
            attributions = ig.attribute(
                input_data, 
                target=target_class,  # Specify which output neuron to compute gradients for
                n_steps=50, 
                internal_batch_size=1
            )
        else:
            # Option 2: Use the predicted class as target
            with torch.no_grad():
                outputs = model_wrapper(input_data)
                predicted_class = outputs.argmax(dim=-1)
            
            attributions = ig.attribute(
                input_data,
                target=predicted_class,
                n_steps=50,
                internal_batch_size=1
            )
        
        return attributions
    
    def occlusion_analysis(self, input_data, target_task=0, target_class=None, window_size=100):
        """Analyze importance of different time windows"""
        
        def model_wrapper(x):
            outputs = self.model(x)
            if isinstance(outputs, list):
                return outputs[target_task]
            return outputs
        
        occlusion = Occlusion(model_wrapper)
        
        # Option 1: Specify target class
        if target_class is not None:
            attributions = occlusion.attribute(
                input_data,
                target=target_class,
                strides=(1, window_size),
                sliding_window_shapes=(1, window_size),
                baselines=0
            )
        else:
            # Option 2: Use predicted class as target
            with torch.no_grad():
                outputs = model_wrapper(input_data)
                predicted_class = outputs.argmax(dim=-1)
            
            attributions = occlusion.attribute(
                input_data,
                target=predicted_class,
                strides=(1, window_size),
                sliding_window_shapes=(1, window_size),
                baselines=0
            )
        
        return attributions
    
    def get_model_predictions(self, input_data, target_task=0):
        """Helper method to get predictions and suggested targets"""
        with torch.no_grad():
            outputs = self.model(input_data)
            if isinstance(outputs, list):
                task_output = outputs[target_task]
            else:
                task_output = outputs
            
            # Get top predictions
            if len(task_output.shape) > 1 and task_output.shape[-1] > 1:
                probs = torch.softmax(task_output, dim=-1)
                top_classes = torch.topk(probs, k=min(5, task_output.shape[-1]), dim=-1)
                return {
                    'predictions': task_output,
                    'probabilities': probs,
                    'top_classes': top_classes.indices,
                    'top_probs': top_classes.values
                }
            else:
                return {
                    'predictions': task_output,
                    'raw_output': task_output
                }

    # Usage example:
    def analyze_model_with_proper_targets():
        # Initialize your model and data
        analyzer = AdvancedModelAnalyzer(model, device)
        
        # Get predictions first to understand the output structure
        pred_info = analyzer.get_model_predictions(input_data, target_task=0)
        print("Model output shape:", pred_info['predictions'].shape)
        
        if 'top_classes' in pred_info:
            print("Top predicted classes:", pred_info['top_classes'])
            # Use the top predicted class as target
            target_class = pred_info['top_classes'][0, 0].item()  # Top prediction for first sample
        else:
            # For regression or single output, you might need a different approach
            target_class = 0
        
        # Now run attribution with proper target
        attributions = analyzer.integrated_gradients_analysis(
            input_data, 
            target_task=0, 
            target_class=target_class
        )
        
        return attributions
