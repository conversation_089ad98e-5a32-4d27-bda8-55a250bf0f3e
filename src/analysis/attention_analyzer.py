import torch
import matplotlib.pyplot as plt
import numpy as np

class AttentionAnalyzer:
    def __init__(self, model):
        self.model = model
        
    def compute_gradient_based_importance(self, input_data, target_task=0):
        """Compute input importance using gradients"""
        input_data.requires_grad_(True)
        
        predictions = self.model(input_data)
        
        # Focus on specific task
        if isinstance(predictions, list):
            target_output = predictions[target_task].mean()
        else:
            target_output = predictions.mean()
        
        # Compute gradients
        target_output.backward()
        
        # Get importance scores
        importance = torch.abs(input_data.grad).mean(dim=0)
        
        return importance.detach()
    
    def analyze_temporal_patterns(self, dataloader, device, task_idx=0):
        """Analyze what temporal patterns the model focuses on"""
        self.model.eval()
        all_importances = []
        
        for batch_x, _ in dataloader:
            batch_x = batch_x.to(device)
            
            importance = self.compute_gradient_based_importance(batch_x, task_idx)
            all_importances.append(importance.cpu())
        
        # Average importance across batches
        avg_importance = torch.stack(all_importances).mean(dim=0)
        
        return avg_importance
    
    def plot_temporal_importance(self, importance_scores, sampling_rate=100):
        """Plot temporal importance patterns"""
        time_axis = np.arange(len(importance_scores)) / sampling_rate
        
        plt.figure(figsize=(12, 6))
        plt.plot(time_axis, importance_scores.numpy())
        plt.xlabel('Time (seconds)')
        plt.ylabel('Importance Score')
        plt.title('Temporal Importance Pattern')
        plt.grid(True, alpha=0.3)
        
        return plt.gcf()