import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from captum.attr import (
    IntegratedGradients, 
    Saliency,
    Occlusion
)
from pathlib import Path
import pandas as pd

class CaptumModelAnalyzer:
    def __init__(self, model, device, task_names=['Sleep', 'Arousal', 'Apnea', 'LM'], sampling_rate=100):
        self.model = model
        self.device = device
        self.task_names = task_names
        self.model.eval()
        
        # Create output directory
        self.output_dir = Path("captum_analysis")
        self.output_dir.mkdir(exist_ok=True)
        
        # PSG channel information
        self.channel_names = [
            'EEG',
            'EOG', 
            'EMG Chin', 
            'EMG Leg', 
            'Position', 
            'Snore', 
            'Chest Effort', 
            'Abdominal Effort', 
            'SpO2',  
            'Airflow'
        ]
        
        self.channel_groups = {
            'EEG': [0],  # Brain activity
            'EOG': [1],  # Eye movements
            'EMG': [2, 3],  # Muscle activity (<PERSON>, Leg)
            'Respiratory': [5, 6, 7, 9],  # Breathing signals (<PERSON>nor<PERSON>, Chest, Abdominal, Airflow)
            'Physiological': [4, 8]  # Position, SpO2
        }
        
        # Sleep staging: 30-second epochs
        # Events: 1-second resolution
        self.sleep_epoch_duration = 30  # seconds
        self.event_resolution = 1  # seconds
        self.sampling_rate = sampling_rate  # Hz
        
    def debug_model_behavior(self, input_data, sample_labels=None):
        """Understand what the model actually outputs from PSG data"""
        print("\n=== PSG MODEL BEHAVIOR ANALYSIS ===")

        self.model.eval()
        with torch.no_grad():
            outputs = self.model(input_data)

        print(f"PSG Input shape: {input_data.shape}")  # [B, Channels, Time]
        print(f"Number of PSG channels: {input_data.shape[1]}")
        print(f"Total recording duration: {input_data.shape[-1] / self.sampling_rate / 3600:.1f} hours")

        # Determine valid data range based on labels
        valid_mask = None
        if sample_labels is not None and len(sample_labels) > 0:
            sleep_labels = sample_labels[0]  # Sleep stage labels
            if len(sleep_labels.shape) > 1:
                sleep_labels = sleep_labels.squeeze()

            # Find valid epochs (where labels != -1)
            valid_epochs = sleep_labels != -1
            valid_epoch_count = torch.sum(valid_epochs).item()

            if valid_epoch_count > 0:
                valid_duration = valid_epoch_count * self.sleep_epoch_duration / 3600
                print(f"Valid sleep data: {valid_epoch_count} epochs ({valid_duration:.1f} hours)")
                print(f"Padding: {len(sleep_labels) - valid_epoch_count} epochs")

                # Create mask for valid time samples
                valid_start_sample = 0
                valid_end_sample = valid_epoch_count * self.sleep_epoch_duration * self.sampling_rate
                valid_mask = (valid_start_sample, min(valid_end_sample, input_data.shape[-1]))
                print(f"Valid signal range: samples {valid_start_sample} to {valid_end_sample}")
            else:
                print("⚠️ No valid sleep data found (all labels are -1)")
        else:
            print("⚠️ No labels provided - analyzing full signal (may include padding)")
        
        # Show which channels are present
        n_channels = min(input_data.shape[1], len(self.channel_names))
        print(f"\nPSG Channels analyzed:")
        for i in range(n_channels):
            channel_name = self.channel_names[i] if i < len(self.channel_names) else f"Channel_{i}"
            signal_std = input_data[0, i].std().item()
            print(f"  {i:2d}: {channel_name:<20} (signal std: {signal_std:.3f})")
        
        if isinstance(outputs, list):
            for i, output in enumerate(outputs):
                task_name = self.task_names[i] if i < len(self.task_names) else f"Task_{i}"
                print(f"\n{task_name} Task:")
                print(f"  Output shape: {output.shape}")
                
                if task_name == 'Sleep':
                    n_epochs = output.shape[1]
                    duration_hours = n_epochs * self.sleep_epoch_duration / 3600
                    print(f"  Sleep epochs: {n_epochs} ({duration_hours:.1f} hours)")
                    print(f"  Classes: {output.shape[-1]} (Wake, N1, N2, N3, REM)")

                    # Show sleep stage distribution (only for valid epochs)
                    probs = torch.softmax(output, dim=-1)
                    predicted_stages = torch.argmax(probs, dim=-1)

                    if valid_mask and sample_labels is not None:
                        # Only analyze valid epochs
                        sleep_labels = sample_labels[0].squeeze()
                        valid_epochs = sleep_labels != -1
                        valid_predictions = predicted_stages[0][valid_epochs]
                        valid_count = len(valid_predictions)

                        stage_counts = torch.bincount(valid_predictions.flatten(), minlength=5)
                        stage_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
                        print(f"  Valid epoch analysis ({valid_count} epochs):")
                        for j, (stage, count) in enumerate(zip(stage_names, stage_counts)):
                            percentage = count.item() / valid_count * 100 if valid_count > 0 else 0
                            print(f"    {stage}: {count.item()} epochs ({percentage:.1f}%)")
                    else:
                        # Analyze all epochs (may include padding)
                        stage_counts = torch.bincount(predicted_stages.flatten(), minlength=5)
                        stage_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
                        print(f"  All epoch analysis (may include padding):")
                        for j, (stage, count) in enumerate(zip(stage_names, stage_counts)):
                            percentage = count.item() / n_epochs * 100
                            print(f"    {stage}: {count.item()} epochs ({percentage:.1f}%)")
                
                else:
                    n_seconds = output.shape[1]
                    duration_hours = n_seconds / 3600
                    print(f"  Time points: {n_seconds} seconds ({duration_hours:.1f} hours)")
                    
                    if output.shape[-1] == 1:
                        # Binary classification
                        probs = torch.sigmoid(output)
                        events = (probs > 0.5).sum().item()
                        event_percentage = events / n_seconds * 100
                        print(f"  Detected events: {events} ({event_percentage:.2f}% of time)")
                        print(f"  Mean probability: {probs.mean().item():.4f}")
                    else:
                        # Multi-class
                        probs = torch.softmax(output, dim=-1)
                        predicted_classes = torch.argmax(probs, dim=-1)
                        for class_idx in range(output.shape[-1]):
                            count = (predicted_classes == class_idx).sum().item()
                            percentage = count / n_seconds * 100
                            print(f"    Class {class_idx}: {count} seconds ({percentage:.2f}%)")
        
        return outputs, valid_mask
    
    def analyze_sleep_staging(self, input_data):
        """Analyze what PSG signals drive sleep stage predictions"""
        print("\n=== SLEEP STAGING ANALYSIS ===")
        
        task_idx = 0  # Sleep task
        
        # Get sleep predictions
        with torch.no_grad():
            outputs = self.model(input_data)
            sleep_output = outputs[task_idx]  # [B, Epochs, 5]
            stage_probs = torch.softmax(sleep_output, dim=-1)
            predicted_stages = torch.argmax(stage_probs, dim=-1)
        
        stage_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
        stage_attributions = {}
        
        # Analyze each sleep stage
        for stage_idx, stage_name in enumerate(stage_names):
            print(f"\nAnalyzing {stage_name} stage...")
            
            # Find epochs where this stage is predicted with high confidence
            stage_mask = (predicted_stages[0] == stage_idx)
            stage_confidence = stage_probs[0, stage_mask, stage_idx]
            
            if stage_mask.sum() == 0:
                print(f"  No {stage_name} epochs detected")
                continue
            
            high_conf_epochs = torch.where(stage_mask)[0][stage_confidence > 0.7]
            
            if len(high_conf_epochs) == 0:
                print(f"  No high-confidence {stage_name} epochs")
                continue
            
            # Analyze a few representative epochs
            np.random.shuffle(high_conf_epochs)
            sample_epochs = high_conf_epochs[:3]  # Take first 3
            epoch_attributions = []
            
            for epoch_idx in sample_epochs:
                print(f"  Analyzing epoch {epoch_idx.item()} ({epoch_idx.item() * 0.5:.1f} min)")
                
                # Create wrapper for this specific epoch and stage
                def stage_wrapper(x):
                    outputs = self.model(x)
                    return outputs[task_idx][:, epoch_idx, stage_idx]
                
                # Compute attributions
                ig = IntegratedGradients(stage_wrapper)
                attr = ig.attribute(input_data, baselines=torch.zeros_like(input_data), n_steps=30, internal_batch_size=1)
                epoch_attributions.append(attr.detach().cpu().numpy())
            
            if epoch_attributions:
                # Average attributions across sample epochs
                avg_attribution = np.mean(epoch_attributions, axis=0)
                stage_attributions[stage_name] = avg_attribution
                
                # Analyze which PSG channels are most important
                self._analyze_channel_importance(avg_attribution, f"{stage_name} Sleep Stage")
        
        # Visualize sleep stage attributions
        self._visualize_sleep_attributions(stage_attributions, input_data)
        
        return stage_attributions
    
    def analyze_event_detection(self, input_data, task_idx, task_name, event_threshold=0.5):
        """Analyze what PSG signals drive event detection (Arousal, Apnea, LM)"""
        print(f"\n=== {task_name.upper()} EVENT ANALYSIS ===")
        
        # Get event predictions
        with torch.no_grad():
            outputs = self.model(input_data)
            event_output = outputs[task_idx]  # [B, Seconds, Classes]
            
            if event_output.shape[-1] == 1:
                # Binary classification
                event_probs = torch.sigmoid(event_output).squeeze(-1)
                event_predictions = event_probs > event_threshold
            else:
                # Multi-class (like Apnea with different types)
                event_probs = torch.softmax(event_output, dim=-1)
                event_predictions = torch.argmax(event_probs, dim=-1)
        
        # Find detected events
        if event_output.shape[-1] == 1:
            # Binary events
            event_times = torch.where(event_predictions[0])[0]
            print(f"Detected {len(event_times)} {task_name.lower()} events")
            
            if len(event_times) == 0:
                print(f"No {task_name.lower()} events detected")
                return {}
            
            # Analyze individual events
            event_attributions = []
            sample_events = event_times[:5]  # Analyze first 5 events
            
            for i, event_time in enumerate(sample_events):
                event_time_sec = event_time.item()
                print(f"  Analyzing event {i+1} at {event_time_sec//3600:02d}:{(event_time_sec%3600)//60:02d}:{event_time_sec%60:02d}")
                
                # Create wrapper for this specific event time
                def event_wrapper(x):
                    outputs = self.model(x)
                    return outputs[task_idx][:, event_time, 0]
                
                # Compute attributions
                ig = IntegratedGradients(event_wrapper)
                attr = ig.attribute(input_data, baselines=torch.zeros_like(input_data), n_steps=30, internal_batch_size=1)
                event_attributions.append(attr.detach().cpu().numpy())
            
            # Average attributions across events
            if event_attributions:
                avg_attribution = np.mean(event_attributions, axis=0)
                self._analyze_channel_importance(avg_attribution, f"{task_name} Events")
                return {f'{task_name}_events': avg_attribution}
        
        else:
            # Multi-class events (e.g., different types of apnea)
            class_attributions = {}
            
            for class_idx in range(event_output.shape[-1]):
                class_events = torch.where(event_predictions[0] == class_idx)[0]
                
                if len(class_events) > 0:
                    print(f"  Class {class_idx}: {len(class_events)} events")
                    
                    # Analyze sample events for this class
                    np.random.shuffle(class_events)
                    sample_events = class_events[:3]
                    class_attrs = []
                    
                    for event_time in sample_events:
                        def class_wrapper(x):
                            outputs = self.model(x)
                            return outputs[task_idx][:, event_time, class_idx]
                        
                        ig = IntegratedGradients(class_wrapper)
                        attr = ig.attribute(input_data, baselines=torch.zeros_like(input_data), n_steps=30, internal_batch_size=1)
                        class_attrs.append(attr.detach().cpu().numpy())
                    
                    if class_attrs:
                        avg_attr = np.mean(class_attrs, axis=0)
                        class_attributions[f'{task_name}_class_{class_idx}'] = avg_attr
                        self._analyze_channel_importance(avg_attr, f"{task_name} Class {class_idx}")
            
            return class_attributions
        
        return {}
    
    def analyze_channel_contributions(self, input_data):
        """Analyze which PSG channels contribute most to each task"""
        print("\n=== PSG CHANNEL CONTRIBUTION ANALYSIS ===")
        
        channel_contributions = {}
        
        for task_idx, task_name in enumerate(self.task_names):
            if task_idx >= len(self.model.multiClassNums):
                continue
                
            print(f"\nAnalyzing {task_name} channel contributions...")
            
            # Create simple task wrapper
            def task_wrapper(x):
                outputs = self.model(x)
                task_output = outputs[task_idx]
                if task_output.shape[-1] > 1:
                    return torch.softmax(task_output, dim=-1).max(dim=-1)[0].mean(axis=1)
                else:
                    return torch.sigmoid(task_output.squeeze(-1)).mean(axis=1)
            
            # Compute attributions
            ig = IntegratedGradients(task_wrapper)
            attr = ig.attribute(input_data, baselines=torch.zeros_like(input_data), n_steps=30, internal_batch_size=1)
            
            # Analyze channel-wise contributions
            channel_importance = []
            n_channels = min(attr.shape[1], len(self.channel_names))
            
            for ch in range(n_channels):
                channel_attr = np.abs(attr[0, ch].cpu().numpy())
                importance = np.mean(channel_attr)
                channel_importance.append((self.channel_names[ch], importance))
            
            # Sort by importance
            channel_importance.sort(key=lambda x: x[1], reverse=True)
            channel_contributions[task_name] = channel_importance
            
            print(f"  Top 5 channels for {task_name}:")
            for i, (ch_name, importance) in enumerate(channel_importance[:5]):
                print(f"    {i+1}. {ch_name:<20}: {importance:.6f}")
        
        # Visualize channel contributions
        self._visualize_channel_contributions(channel_contributions)
        
        return channel_contributions
    
    def comprehensive_analysis(self, input_data, sample_labels=None):
        """Run all PSG analyses"""
        print("="*60)
        print("COMPREHENSIVE PSG MODEL ANALYSIS")
        print("="*60)

        # 1. Understand model behavior and get valid data range
        model_outputs, valid_mask = self.debug_model_behavior(input_data, sample_labels)
        
        # 2. Sleep staging analysis
        sleep_attributions = self.analyze_sleep_staging(input_data)
        
        # 3. Event detection analyses
        all_attributions = {}
        all_attributions.update(sleep_attributions)
        
        # Arousal events
        arousal_attrs = self.analyze_event_detection(input_data, 1, "Arousal")
        all_attributions.update(arousal_attrs)
        
        # Apnea events
        apnea_attrs = self.analyze_event_detection(input_data, 2, "Apnea")
        all_attributions.update(apnea_attrs)
        
        # Limb movement events
        lm_attrs = self.analyze_event_detection(input_data, 3, "LM")
        all_attributions.update(lm_attrs)
        
        # 4. Channel contribution analysis
        channel_contributions = self.analyze_channel_contributions(input_data)
        
        # 5. PSG signal group analysis
        self.analyze_signal_groups(input_data)
        
        # 6. Generate comprehensive visualizations
        print("\n" + "="*60)
        print("GENERATING COMPREHENSIVE VISUALIZATIONS")
        print("="*60)

        # # Create hypnogram with events overlay
        # self.create_hypnogram_with_events(input_data, sample_labels, valid_mask)

        # # Create PSG signal overview
        # self.create_psg_signal_overview(input_data, sample_labels, valid_mask)

        # # # Create event detection analysis
        # self.create_event_detection_analysis(input_data, sample_labels, valid_mask)

        # # Create attribution heatmaps
        # self.create_attribution_heatmaps(input_data, sample_labels, valid_mask)

        # # Create sleep transition analysis
        # self.create_sleep_transition_analysis(input_data, sample_labels, valid_mask)

        # Create new advanced visualizations
        print("\n" + "="*60)
        print("CREATING ADVANCED CONTEXT VISUALIZATIONS")
        print("="*60)

        # Sleep stage context analysis (5 before + current + 1 after)
        self.create_sleep_stage_context_analysis(input_data, sample_labels, valid_mask)

        # Event context analysis (30s before + 10s after)
        self.create_event_context_analysis(input_data, sample_labels, valid_mask)

        # Architecture activation analysis
        self.create_architecture_activation_analysis(input_data, sample_labels, valid_mask)

        # 7. Generate summary
        self._generate_psg_summary(all_attributions, channel_contributions)

        return all_attributions
    
    def analyze_signal_groups(self, input_data):
        """Analyze contributions from different PSG signal groups"""
        print("\n=== PSG SIGNAL GROUP ANALYSIS ===")
        
        for task_idx, task_name in enumerate(self.task_names):
            if task_idx >= len(self.model.multiClassNums):
                continue
                
            print(f"\n{task_name} Task - Signal Group Importance:")
            
            # Create task wrapper
            def task_wrapper(x):
                outputs = self.model(x)
                task_output = outputs[task_idx]
                if task_output.shape[-1] > 1:
                    return torch.softmax(task_output, dim=-1).max(dim=-1)[0].mean(axis=1)
                else:
                    return torch.sigmoid(task_output.squeeze(-1)).mean(axis=1)
            
            # Compute attributions
            ig = IntegratedGradients(task_wrapper)
            attr = ig.attribute(input_data, baselines=torch.zeros_like(input_data), n_steps=30, internal_batch_size=1)
            
            # Analyze each signal group
            group_importance = {}
            for group_name, channels in self.channel_groups.items():
                if channels and max(channels) < attr.shape[1]:
                    group_attr = attr[0, channels].cpu().numpy()
                    importance = np.mean(np.abs(group_attr))
                    group_importance[group_name] = importance
                    
                    print(f"  {group_name:<15}: {importance:.6f}")
                    
                    # Clinical interpretation
                    if group_name == 'EEG' and task_name == 'Sleep':
                        print(f"    → EEG patterns drive sleep stage classification")
                    elif group_name == 'Respiratory' and task_name == 'Apnea':
                        print(f"    → Respiratory signals detect apnea events")
                    elif group_name == 'EMG' and task_name == 'Arousal':
                        print(f"    → Muscle activity indicates arousal events")
                    elif group_name == 'EMG' and task_name == 'LM':
                        print(f"    → EMG signals detect limb movements")
            
            # Show relative importance
            if group_importance:
                total_importance = sum(group_importance.values())
                print(f"  Relative contributions:")
                for group, importance in sorted(group_importance.items(), key=lambda x: x[1], reverse=True):
                    percentage = (importance / total_importance) * 100
                    print(f"    {group:<15}: {percentage:.1f}%")
    
    def _analyze_channel_importance(self, attribution, analysis_name):
        """Analyze which PSG channels are most important"""
        print(f"    PSG Channel Analysis for {analysis_name}:")
        
        if len(attribution.shape) >= 3:  # [B, C, T]
            n_channels = min(attribution.shape[1], len(self.channel_names))
            channel_importance = []
            
            for ch in range(n_channels):
                channel_attr = np.abs(attribution[0, ch])
                importance = np.mean(channel_attr)
                channel_importance.append((self.channel_names[ch], importance))
            
            # Sort by importance
            channel_importance.sort(key=lambda x: x[1], reverse=True)
            
            print(f"      Top 5 PSG channels:")
            for i, (ch_name, importance) in enumerate(channel_importance[:5]):
                print(f"        {i+1}. {ch_name:<20}: {importance:.6f}")
                
                # Add clinical context
                if 'EEG' in ch_name and 'Sleep' in analysis_name:
                    print(f"           → Brain activity patterns for sleep staging")
                elif 'EOG' in ch_name and ('REM' in analysis_name or 'Wake' in analysis_name):
                    print(f"           → Eye movement patterns")
                elif 'EMG' in ch_name and 'Arousal' in analysis_name:
                    print(f"           → Muscle tension changes")
                elif 'Flow' in ch_name and 'Apnea' in analysis_name:
                    print(f"           → Breathing disruption patterns")
                elif 'SpO2' in ch_name and 'Apnea' in analysis_name:
                    print(f"           → Oxygen desaturation events")
    
    def _visualize_sleep_attributions(self, stage_attributions, input_data):
        """Visualize sleep stage attributions across PSG channels"""
        if not stage_attributions:
            return
        
        n_stages = len(stage_attributions)
        fig, axes = plt.subplots(n_stages + 1, 1, figsize=(15, 3 * (n_stages + 1)))
        
        if n_stages == 1:
            axes = [axes, axes]
        
        # Time axis for full night (assuming 100 Hz sampling)
        time_hours = np.arange(input_data.shape[-1]) / (self.sampling_rate * 3600)
        
        # Plot sample PSG signals (first few channels, downsampled)
        downsample_factor = 1000  # Show every 10th second
        time_ds = time_hours[::downsample_factor]
        
        # Show EEG, EOG, EMG signals
        signal_channels = [0, 1, 2]  # EEG F3-M2, EOG L, EMG Chin
        signal_names = ['EEG', 'EOG', 'EMG']
        
        for i, (ch, name) in enumerate(zip(signal_channels, signal_names)):
            if ch < input_data.shape[1]:
                signal_ds = input_data[0, ch, ::downsample_factor].cpu().numpy()
                axes[0].plot(time_ds[:len(signal_ds)], signal_ds + i*2, 
                           label=name, alpha=0.7, linewidth=0.5)
        
        axes[0].set_title('Sample PSG Signals Throughout Night')
        axes[0].set_ylabel('Signal Amplitude')
        axes[0].set_xlabel('Time (hours)')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # Plot attributions for each sleep stage
        colors = ['red', 'orange', 'blue', 'purple', 'green']  # Wake, N1, N2, N3, REM
        
        for idx, (stage_name, attr) in enumerate(stage_attributions.items()):
            if idx + 1 >= len(axes):
                break
                
            ax = axes[idx + 1]
            
            # Average attribution across all channels
            if len(attr.shape) > 2:
                attr_plot = np.mean(np.abs(attr[0]), axis=0)  # Average across channels
            elif len(attr.shape) == 2:
                attr_plot = np.abs(attr[0])
            else:
                attr_plot = np.abs(attr)
            
            # Downsample attribution for visualization
            attr_ds = attr_plot[::downsample_factor]
            time_attr = time_hours[::downsample_factor][:len(attr_ds)]
            
            ax.plot(time_attr, attr_ds, color=colors[idx % len(colors)], linewidth=1.5)
            ax.fill_between(time_attr, 0, attr_ds, alpha=0.3, color=colors[idx % len(colors)])
            ax.set_title(f'{stage_name} Stage Attribution (All PSG Channels)')
            ax.set_ylabel('Attribution Magnitude')
            ax.set_xlabel('Time (hours)')
            ax.grid(True, alpha=0.3)
            
            # Add statistics
            stats_text = f'Mean: {np.mean(attr_plot):.4f}\nStd: {np.std(attr_plot):.4f}'
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes,
                   verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'psg_sleep_stage_attributions.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def _visualize_channel_contributions(self, channel_contributions):
        """Visualize PSG channel contributions for each task"""
        if not channel_contributions:
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        axes = axes.flatten()
        
        for idx, (task_name, contributions) in enumerate(channel_contributions.items()):
            if idx >= len(axes):
                break
                
            ax = axes[idx]
            
            # Get top 10 channels
            top_channels = contributions[:10]
            channels, importances = zip(*top_channels)
            
            # Create horizontal bar plot
            y_pos = np.arange(len(channels))
            bars = ax.barh(y_pos, importances)
            
            # Color bars by channel type
            colors = []
            for ch in channels:
                if 'EEG' in ch:
                    colors.append('blue')
                elif 'EOG' in ch:
                    colors.append('green')
                elif 'EMG' in ch:
                    colors.append('red')
                elif any(resp in ch for resp in ['Flow', 'Chest', 'Abdominal', 'Snore']):
                    colors.append('orange')
                elif 'SpO2' in ch:
                    colors.append('purple')
                else:
                    colors.append('gray')
            
            for bar, color in zip(bars, colors):
                bar.set_color(color)
            
            ax.set_yticks(y_pos)
            ax.set_yticklabels(channels)
            ax.invert_yaxis()
            ax.set_xlabel('Attribution Importance')
            ax.set_title(f'{task_name} Task - Top PSG Channels')
            ax.grid(True, alpha=0.3)
        
        # Add legend
        legend_elements = [
            plt.Rectangle((0,0),1,1, facecolor='blue', label='EEG'),
            plt.Rectangle((0,0),1,1, facecolor='green', label='EOG'),
            plt.Rectangle((0,0),1,1, facecolor='red', label='EMG'),
            plt.Rectangle((0,0),1,1, facecolor='orange', label='Respiratory'),
            plt.Rectangle((0,0),1,1, facecolor='purple', label='SpO2')
        ]
        fig.legend(handles=legend_elements, loc='upper right')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'psg_channel_contributions.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def _generate_psg_summary(self, all_attributions, channel_contributions):
        """Generate PSG-specific analysis summary"""
        print("\n" + "="*60)
        print("PSG MODEL ANALYSIS SUMMARY")
        print("="*60)
        
        print(f"\nAnalyzed {len(all_attributions)} different sleep/event patterns:")
        
        for name, attr in all_attributions.items():
            attr_flat = attr.flatten()
            importance_score = np.std(np.abs(attr_flat))
            
            print(f"\n{name}:")
            print(f"  Importance score: {importance_score:.6f}")
            print(f"  Attribution range: [{np.min(attr_flat):.4f}, {np.max(attr_flat):.4f}]")
        
        # PSG Channel insights
        print(f"\n" + "="*40)
        print("PSG CHANNEL INSIGHTS")
        print("="*40)
        
        for task_name, contributions in channel_contributions.items():
            print(f"\n{task_name} Task - Key PSG Signals:")
            top_3 = contributions[:3]
            for i, (channel, importance) in enumerate(top_3, 1):
                print(f"  {i}. {channel}: {importance:.6f}")
                
                # Clinical interpretation
                if 'EEG' in channel and task_name == 'Sleep':
                    print(f"     → Brain wave patterns essential for sleep staging")
                elif 'Flow' in channel and task_name == 'Apnea':
                    print(f"     → Airflow disruption indicates apnea events")
                elif 'EMG' in channel and task_name == 'Arousal':
                    print(f"     → Muscle activation signals arousal")
                elif 'SpO2' in channel and task_name == 'Apnea':
                    print(f"     → Oxygen drops confirm apnea severity")
        
        print(f"\n" + "="*40)
        print("CLINICAL INSIGHTS")
        print("="*40)
        print(f"• Model successfully integrates multiple PSG modalities")
        print(f"• EEG signals dominate sleep stage classification")
        print(f"• Respiratory signals are key for apnea detection")
        print(f"• EMG activity helps identify arousals and movements")
        print(f"• Multi-modal approach enables robust sleep analysis")
        
        print(f"\nDetailed PSG visualizations saved to: {self.output_dir}")

    def group_consecutive_events(self, event_predictions):
        """Group consecutive event seconds into single events"""
        events = []
        if len(event_predictions) == 0:
            return events

        # Find consecutive event periods
        event_indices = np.where(event_predictions)[0]
        if len(event_indices) == 0:
            return events

        # Group consecutive indices
        current_event_start = event_indices[0]
        current_event_end = event_indices[0]

        for i in range(1, len(event_indices)):
            if event_indices[i] == current_event_end + 1:
                # Consecutive, extend current event
                current_event_end = event_indices[i]
            else:
                # Gap found, save current event and start new one
                events.append((current_event_start, current_event_end))
                current_event_start = event_indices[i]
                current_event_end = event_indices[i]

        # Add the last event
        events.append((current_event_start, current_event_end))

        return events

    def create_hypnogram_with_events(self, input_data, sample_labels=None, valid_mask=None, save_name="hypnogram_with_events"):
        """Create comprehensive hypnogram with event overlays"""
        print("\n=== CREATING HYPNOGRAM WITH EVENTS ===")

        with torch.no_grad():
            outputs = self.model(input_data)

        # Extract sleep stages
        sleep_output = outputs[0]  # [B, Epochs, 5]
        stage_probs = torch.softmax(sleep_output, dim=-1)
        predicted_stages = torch.argmax(stage_probs, dim=-1)[0].cpu().numpy()
        stage_confidence = torch.max(stage_probs, dim=-1)[0][0].cpu().numpy()

        # Determine valid epochs
        valid_epochs = None
        if sample_labels is not None and len(sample_labels) > 0:
            sleep_labels = sample_labels[0].squeeze()
            valid_epochs = (sleep_labels != -1).cpu().numpy()
            print(f"Valid epochs: {np.sum(valid_epochs)}/{len(valid_epochs)}")

            # Only analyze valid epochs
            predicted_stages = predicted_stages[valid_epochs]
            stage_confidence = stage_confidence[valid_epochs]
        else:
            print("⚠️ No labels provided - analyzing all epochs (may include padding)")

        # Extract events
        events_data = {}
        event_names = ['Arousal', 'Apnea', 'LM']
        for i, event_name in enumerate(event_names):
            if i + 1 < len(outputs):
                event_output = outputs[i + 1]
                if event_output.shape[-1] == 1:
                    event_probs = torch.sigmoid(event_output).squeeze(-1)[0].cpu().numpy()
                    event_predictions = event_probs > 0.5
                else:
                    event_probs = torch.softmax(event_output, dim=-1)
                    event_predictions = torch.argmax(event_probs, dim=-1)[0].cpu().numpy()

                # Trim events to valid data range if available
                if valid_mask is not None:
                    valid_start, valid_end = valid_mask
                    valid_end_seconds = min(valid_end // self.sampling_rate, len(event_predictions))
                    event_predictions = event_predictions[:valid_end_seconds]
                    print(f"Trimmed {event_name} events to {len(event_predictions)} seconds (valid data)")

                events_data[event_name] = event_predictions

        # Create comprehensive visualization
        fig, axes = plt.subplots(4, 1, figsize=(20, 12))

        # Time axes
        epoch_times = np.arange(len(predicted_stages)) * 0.5 / 60  # Convert to hours
        event_times = np.arange(len(list(events_data.values())[0])) / 3600  # Convert to hours

        # 1. Hypnogram with confidence
        stage_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
        stage_colors = ['red', 'orange', 'lightblue', 'blue', 'green']

        # Plot sleep stages with confidence-based alpha
        for i, (stage, color) in enumerate(zip(stage_names, stage_colors)):
            mask = predicted_stages == i
            if np.any(mask):
                confidence_alpha = stage_confidence[mask]
                for j, (time, conf) in enumerate(zip(epoch_times[mask], confidence_alpha)):
                    axes[0].scatter(time, i, c=color, alpha=min(1.0, conf + 0.3), s=20)

        axes[0].set_ylabel('Sleep Stage')
        axes[0].set_yticks(range(5))
        axes[0].set_yticklabels(stage_names)
        axes[0].set_title('Sleep Stages (Hypnogram) with Model Confidence')
        axes[0].grid(True, alpha=0.3)
        axes[0].invert_yaxis()

        # 2. Event timeline
        event_colors = ['red', 'blue', 'purple']
        y_positions = [0.8, 0.5, 0.2]

        for (event_name, event_data), color, y_pos in zip(events_data.items(), event_colors, y_positions):
            event_indices = np.where(event_data)[0]
            if len(event_indices) > 0:
                event_hours = event_indices / 3600
                axes[1].scatter(event_hours, [y_pos] * len(event_hours),
                              c=color, label=event_name, alpha=0.7, s=15)

        axes[1].set_ylabel('Events')
        axes[1].set_ylim(0, 1)
        axes[1].set_yticks([0.2, 0.5, 0.8])
        axes[1].set_yticklabels(['LM', 'Apnea', 'Arousal'])
        axes[1].set_title('Sleep Events Timeline')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)

        # 3. Sleep architecture analysis
        stage_durations = []
        stage_percentages = []
        total_epochs = len(predicted_stages)

        for i, stage in enumerate(stage_names):
            count = np.sum(predicted_stages == i)
            duration_min = count * 0.5
            percentage = count / total_epochs * 100
            stage_durations.append(duration_min)
            stage_percentages.append(percentage)

        bars = axes[2].bar(stage_names, stage_percentages, color=stage_colors, alpha=0.7)
        axes[2].set_ylabel('Percentage (%)')
        axes[2].set_title('Sleep Architecture Distribution')
        axes[2].grid(True, alpha=0.3)

        # Add percentage labels on bars
        for bar, percentage in zip(bars, stage_percentages):
            height = bar.get_height()
            axes[2].text(bar.get_x() + bar.get_width()/2., height + 0.5,
                        f'{percentage:.1f}%', ha='center', va='bottom')

        # 4. Event rates per hour
        event_rates = {}
        total_hours = len(list(events_data.values())[0]) / 3600

        for event_name, event_data in events_data.items():
            total_events = np.sum(event_data)
            rate_per_hour = total_events / total_hours
            event_rates[event_name] = rate_per_hour

        bars = axes[3].bar(event_rates.keys(), event_rates.values(),
                          color=event_colors, alpha=0.7)
        axes[3].set_ylabel('Events per Hour')
        axes[3].set_title('Event Rates')
        axes[3].grid(True, alpha=0.3)

        # Add rate labels on bars
        for bar, (event_name, rate) in zip(bars, event_rates.items()):
            height = bar.get_height()
            axes[3].text(bar.get_x() + bar.get_width()/2., height + 0.1,
                        f'{rate:.1f}', ha='center', va='bottom')

            # Add clinical interpretation
            if event_name == 'Apnea' and rate >= 5:
                severity = 'Severe' if rate >= 30 else 'Moderate' if rate >= 15 else 'Mild'
                axes[3].text(bar.get_x() + bar.get_width()/2., height/2,
                           f'{severity}', ha='center', va='center',
                           fontweight='bold', color='white')

        # Set common x-axis
        for ax in axes[:2]:
            ax.set_xlim(0, max(epoch_times[-1], event_times[-1]))
            ax.set_xlabel('Time (hours)')

        plt.tight_layout()
        plt.savefig(self.output_dir / f'{save_name}.png', dpi=300, bbox_inches='tight')
        plt.show()

        return fig

    def create_psg_signal_overview(self, input_data, sample_labels=None, valid_mask=None, save_name="psg_signal_overview"):
        """Create comprehensive PSG signal overview with annotations"""
        print("\n=== CREATING PSG SIGNAL OVERVIEW ===")

        # Get model predictions for annotations
        with torch.no_grad():
            outputs = self.model(input_data)

        # Extract predictions
        sleep_output = outputs[0]
        predicted_stages = torch.argmax(torch.softmax(sleep_output, dim=-1), dim=-1)[0].cpu().numpy()

        # Create figure with subplots for each channel group
        fig, axes = plt.subplots(len(self.channel_groups), 1, figsize=(20, 3 * len(self.channel_groups)))
        if len(self.channel_groups) == 1:
            axes = [axes]

        # Time axis (downsample for visualization)
        downsample_factor = 1000  # Show every 10 seconds
        total_samples = input_data.shape[-1]
        time_hours = np.arange(0, total_samples, downsample_factor) / (self.sampling_rate * 3600)

        # Color map for sleep stages
        stage_colors = ['red', 'orange', 'lightblue', 'blue', 'green']
        stage_names = ['Wake', 'N1', 'N2', 'N3', 'REM']

        for ax_idx, (group_name, channels) in enumerate(self.channel_groups.items()):
            ax = axes[ax_idx]

            # Plot signals for this group
            y_offset = 0
            for i, ch in enumerate(channels):
                if ch < input_data.shape[1]:
                    signal = input_data[0, ch, ::downsample_factor].cpu().numpy()
                    signal_normalized = (signal - np.mean(signal)) / (np.std(signal) + 1e-8)

                    ax.plot(time_hours[:len(signal_normalized)],
                           signal_normalized + y_offset,
                           label=self.channel_names[ch],
                           alpha=0.8, linewidth=0.5)
                    y_offset += 3

            # Add sleep stage background coloring
            epoch_duration = 30 / 3600  # 30 seconds in hours
            for epoch_idx, stage in enumerate(predicted_stages):
                start_time = epoch_idx * epoch_duration
                end_time = (epoch_idx + 1) * epoch_duration

                if start_time < time_hours[-1]:
                    ax.axvspan(start_time, min(end_time, time_hours[-1]),
                              alpha=0.2, color=stage_colors[stage])

            ax.set_title(f'{group_name} Signals with Sleep Stage Background')
            ax.set_ylabel('Normalized Amplitude')
            ax.legend(loc='upper right')
            ax.grid(True, alpha=0.3)

            # Add clinical annotations
            if group_name == 'EEG':
                ax.text(0.02, 0.98, 'Brain Activity Patterns', transform=ax.transAxes,
                       verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
            elif group_name == 'Respiratory':
                ax.text(0.02, 0.98, 'Breathing Patterns', transform=ax.transAxes,
                       verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
            elif group_name == 'EMG':
                ax.text(0.02, 0.98, 'Muscle Activity', transform=ax.transAxes,
                       verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))

        # Add sleep stage legend
        legend_elements = [plt.Rectangle((0,0),1,1, facecolor=color, alpha=0.5, label=stage)
                          for color, stage in zip(stage_colors, stage_names)]
        fig.legend(handles=legend_elements, loc='upper center', ncol=5,
                  bbox_to_anchor=(0.5, 0.98), title='Sleep Stages')

        axes[-1].set_xlabel('Time (hours)')
        plt.tight_layout()
        plt.subplots_adjust(top=0.93)
        plt.savefig(self.output_dir / f'{save_name}.png', dpi=300, bbox_inches='tight')
        plt.show()

        return fig

    def create_event_detection_analysis(self, input_data, sample_labels=None, valid_mask=None, save_name="event_detection_analysis"):
        """Create detailed event detection analysis"""
        print("\n=== CREATING EVENT DETECTION ANALYSIS ===")

        with torch.no_grad():
            outputs = self.model(input_data)

        event_names = ['Arousal', 'Apnea', 'LM']
        fig, axes = plt.subplots(len(event_names), 2, figsize=(20, 4 * len(event_names)))

        for i, event_name in enumerate(event_names):
            if i + 1 < len(outputs):
                event_output = outputs[i + 1]

                if event_output.shape[-1] == 1:
                    # Binary classification
                    event_probs = torch.sigmoid(event_output).squeeze(-1)[0].cpu().numpy()
                    event_predictions = event_probs > 0.5
                else:
                    # Multi-class
                    event_probs = torch.softmax(event_output, dim=-1)
                    event_predictions = torch.argmax(event_probs, dim=-1)[0].cpu().numpy()
                    event_probs = torch.max(event_probs, dim=-1)[0][0].cpu().numpy()

                # Time axis
                time_hours = np.arange(len(event_probs)) / 3600

                # Left plot: Probability timeline
                ax_left = axes[i, 0]
                ax_left.plot(time_hours, event_probs, alpha=0.7, linewidth=1)
                ax_left.fill_between(time_hours, 0, event_probs, alpha=0.3)

                # Mark detected events
                if event_output.shape[-1] == 1:
                    event_times = time_hours[event_predictions]
                    if len(event_times) > 0:
                        ax_left.scatter(event_times, event_probs[event_predictions],
                                      color='red', s=20, alpha=0.8, label='Detected Events')

                ax_left.axhline(y=0.5, color='red', linestyle='--', alpha=0.5, label='Threshold')
                ax_left.set_title(f'{event_name} Detection Probability')
                ax_left.set_xlabel('Time (hours)')
                ax_left.set_ylabel('Probability')
                ax_left.legend()
                ax_left.grid(True, alpha=0.3)

                # Right plot: Event distribution and statistics
                ax_right = axes[i, 1]

                if event_output.shape[-1] == 1:
                    # Binary events - show hourly distribution
                    hours = np.arange(int(time_hours[-1]) + 1)
                    hourly_counts = []

                    for hour in hours:
                        hour_mask = (time_hours >= hour) & (time_hours < hour + 1)
                        hourly_count = np.sum(event_predictions[hour_mask])
                        hourly_counts.append(hourly_count)

                    bars = ax_right.bar(hours, hourly_counts, alpha=0.7)
                    ax_right.set_title(f'{event_name} Events per Hour')
                    ax_right.set_xlabel('Hour of Recording')
                    ax_right.set_ylabel('Event Count')

                    # Add clinical thresholds
                    if event_name == 'Apnea':
                        ax_right.axhline(y=5, color='orange', linestyle='--', label='Mild (5/hr)')
                        ax_right.axhline(y=15, color='red', linestyle='--', label='Moderate (15/hr)')
                        ax_right.axhline(y=30, color='darkred', linestyle='--', label='Severe (30/hr)')
                        ax_right.legend()
                    elif event_name == 'Arousal':
                        ax_right.axhline(y=15, color='red', linestyle='--', label='High (>15/hr)')
                        ax_right.legend()

                    # Color bars based on severity
                    if event_name == 'Apnea':
                        for bar, count in zip(bars, hourly_counts):
                            if count >= 30:
                                bar.set_color('darkred')
                            elif count >= 15:
                                bar.set_color('red')
                            elif count >= 5:
                                bar.set_color('orange')

                else:
                    # Multi-class events
                    unique_classes, counts = np.unique(event_predictions, return_counts=True)
                    ax_right.bar(unique_classes, counts, alpha=0.7)
                    ax_right.set_title(f'{event_name} Class Distribution')
                    ax_right.set_xlabel('Event Class')
                    ax_right.set_ylabel('Count')

                ax_right.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(self.output_dir / f'{save_name}.png', dpi=300, bbox_inches='tight')
        plt.show()

        return fig

    def create_attribution_heatmaps(self, input_data, sample_labels=None, valid_mask=None, save_name="attribution_heatmaps"):
        """Create detailed attribution heatmaps for each task and channel"""
        print("\n=== CREATING ATTRIBUTION HEATMAPS ===")

        # Compute attributions for each task
        all_attributions = {}

        for task_idx, task_name in enumerate(self.task_names):
            if task_idx >= len(self.model.multiClassNums):
                continue

            print(f"Computing attributions for {task_name}...")

            # Create task wrapper
            def task_wrapper(x):
                outputs = self.model(x)
                task_output = outputs[task_idx]
                if task_output.shape[-1] > 1:
                    return torch.softmax(task_output, dim=-1).max(dim=-1)[0].mean(axis=1)
                else:
                    return torch.sigmoid(task_output.squeeze(-1)).mean(axis=1)

            # Compute attributions
            ig = IntegratedGradients(task_wrapper)
            attr = ig.attribute(input_data, baselines=torch.zeros_like(input_data),
                               n_steps=30, internal_batch_size=1)
            all_attributions[task_name] = attr.detach().cpu().numpy()

        # Create heatmap visualization
        n_tasks = len(all_attributions)
        fig, axes = plt.subplots(n_tasks, 2, figsize=(20, 5 * n_tasks))
        if n_tasks == 1:
            axes = axes.reshape(1, -1)

        for task_idx, (task_name, attr) in enumerate(all_attributions.items()):
            # Left plot: Channel-wise attribution heatmap
            ax_left = axes[task_idx, 0]

            # Average attribution over time for each channel
            n_channels = min(attr.shape[1], len(self.channel_names))
            channel_attr = np.abs(attr[0, :n_channels, :])

            # Downsample for visualization
            downsample_factor = max(1, channel_attr.shape[1] // 1000)
            channel_attr_ds = channel_attr[:, ::downsample_factor]

            # Create heatmap
            im = ax_left.imshow(channel_attr_ds, aspect='auto', cmap='hot', interpolation='nearest')
            ax_left.set_title(f'{task_name} - Channel Attribution Heatmap')
            ax_left.set_ylabel('PSG Channels')
            ax_left.set_xlabel('Time (downsampled)')

            # Set channel labels
            ax_left.set_yticks(range(n_channels))
            ax_left.set_yticklabels([self.channel_names[i] for i in range(n_channels)])

            # Add colorbar
            plt.colorbar(im, ax=ax_left, label='Attribution Magnitude')

            # Right plot: Temporal attribution pattern
            ax_right = axes[task_idx, 1]

            # Average attribution across all channels
            temporal_attr = np.mean(np.abs(attr[0]), axis=0)

            # Downsample for visualization
            temporal_attr_ds = temporal_attr[::downsample_factor]
            time_hours = np.arange(len(temporal_attr_ds)) * downsample_factor / (self.sampling_rate * 3600)

            ax_right.plot(time_hours, temporal_attr_ds, linewidth=1)
            ax_right.fill_between(time_hours, 0, temporal_attr_ds, alpha=0.3)
            ax_right.set_title(f'{task_name} - Temporal Attribution Pattern')
            ax_right.set_xlabel('Time (hours)')
            ax_right.set_ylabel('Average Attribution')
            ax_right.grid(True, alpha=0.3)

            # Add statistics
            stats_text = f'Mean: {np.mean(temporal_attr):.4f}\nStd: {np.std(temporal_attr):.4f}\nMax: {np.max(temporal_attr):.4f}'
            ax_right.text(0.02, 0.98, stats_text, transform=ax_right.transAxes,
                         verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()
        plt.savefig(self.output_dir / f'{save_name}.png', dpi=300, bbox_inches='tight')
        plt.show()

        return fig

    def create_sleep_transition_analysis(self, input_data, sample_labels=None, valid_mask=None, save_name="sleep_transition_analysis"):
        """Analyze sleep stage transitions and patterns"""
        print("\n=== CREATING SLEEP TRANSITION ANALYSIS ===")

        with torch.no_grad():
            outputs = self.model(input_data)

        sleep_output = outputs[0]
        stage_probs = torch.softmax(sleep_output, dim=-1)
        predicted_stages = torch.argmax(stage_probs, dim=-1)[0].cpu().numpy()
        stage_confidence = torch.max(stage_probs, dim=-1)[0][0].cpu().numpy()

        stage_names = ['Wake', 'N1', 'N2', 'N3', 'REM']

        # Create transition matrix
        n_stages = len(stage_names)
        transition_matrix = np.zeros((n_stages, n_stages))

        for i in range(len(predicted_stages) - 1):
            current_stage = predicted_stages[i]
            next_stage = predicted_stages[i + 1]
            transition_matrix[current_stage, next_stage] += 1

        # Normalize to probabilities
        row_sums = transition_matrix.sum(axis=1, keepdims=True)
        transition_probs = np.divide(transition_matrix, row_sums,
                                   out=np.zeros_like(transition_matrix), where=row_sums!=0)

        # Create visualization
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        # 1. Transition probability matrix
        ax1 = axes[0, 0]
        im1 = ax1.imshow(transition_probs, cmap='Blues', vmin=0, vmax=1)
        ax1.set_title('Sleep Stage Transition Probabilities')
        ax1.set_xlabel('Next Stage')
        ax1.set_ylabel('Current Stage')
        ax1.set_xticks(range(n_stages))
        ax1.set_yticks(range(n_stages))
        ax1.set_xticklabels(stage_names)
        ax1.set_yticklabels(stage_names)

        # Add probability values to cells
        for i in range(n_stages):
            for j in range(n_stages):
                text = ax1.text(j, i, f'{transition_probs[i, j]:.2f}',
                               ha="center", va="center", color="black" if transition_probs[i, j] < 0.5 else "white")

        plt.colorbar(im1, ax=ax1, label='Transition Probability')

        # 2. Sleep cycles analysis
        ax2 = axes[0, 1]

        # Identify REM periods and cycles
        rem_periods = []
        in_rem = False
        rem_start = None

        for i, stage in enumerate(predicted_stages):
            if stage == 4 and not in_rem:  # REM start
                rem_start = i
                in_rem = True
            elif stage != 4 and in_rem:  # REM end
                rem_periods.append((rem_start, i))
                in_rem = False

        if in_rem:  # Handle case where recording ends in REM
            rem_periods.append((rem_start, len(predicted_stages)))

        # Plot sleep cycles
        epoch_times = np.arange(len(predicted_stages)) * 0.5 / 60  # Convert to hours
        ax2.plot(epoch_times, predicted_stages, linewidth=2, alpha=0.7)

        # Highlight REM periods
        for rem_start, rem_end in rem_periods:
            ax2.axvspan(epoch_times[rem_start], epoch_times[rem_end],
                       alpha=0.3, color='green', label='REM Period' if rem_start == rem_periods[0][0] else "")

        ax2.set_title(f'Sleep Cycles (Found {len(rem_periods)} REM periods)')
        ax2.set_xlabel('Time (hours)')
        ax2.set_ylabel('Sleep Stage')
        ax2.set_yticks(range(5))
        ax2.set_yticklabels(stage_names)
        ax2.grid(True, alpha=0.3)
        ax2.legend()

        # 3. Stage duration distribution
        ax3 = axes[1, 0]

        stage_durations = []
        current_stage = predicted_stages[0]
        current_duration = 1

        for i in range(1, len(predicted_stages)):
            if predicted_stages[i] == current_stage:
                current_duration += 1
            else:
                stage_durations.append((current_stage, current_duration * 0.5))  # Convert to minutes
                current_stage = predicted_stages[i]
                current_duration = 1

        # Add final duration
        stage_durations.append((current_stage, current_duration * 0.5))

        # Group by stage
        stage_duration_groups = {i: [] for i in range(5)}
        for stage, duration in stage_durations:
            stage_duration_groups[stage].append(duration)

        # Create box plot
        duration_data = [stage_duration_groups[i] for i in range(5)]
        box_plot = ax3.boxplot(duration_data, labels=stage_names, patch_artist=True)

        # Color boxes
        colors = ['red', 'orange', 'lightblue', 'blue', 'green']
        for patch, color in zip(box_plot['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)

        ax3.set_title('Sleep Stage Duration Distribution')
        ax3.set_ylabel('Duration (minutes)')
        ax3.grid(True, alpha=0.3)

        # 4. Confidence analysis
        ax4 = axes[1, 1]

        # Plot confidence over time
        ax4.plot(epoch_times, stage_confidence, linewidth=1, alpha=0.8)
        ax4.fill_between(epoch_times, 0, stage_confidence, alpha=0.3)
        ax4.axhline(y=0.8, color='green', linestyle='--', alpha=0.7, label='High Confidence (>0.8)')
        ax4.axhline(y=0.6, color='orange', linestyle='--', alpha=0.7, label='Medium Confidence (>0.6)')
        ax4.axhline(y=0.4, color='red', linestyle='--', alpha=0.7, label='Low Confidence (<0.4)')

        ax4.set_title('Model Confidence in Sleep Stage Predictions')
        ax4.set_xlabel('Time (hours)')
        ax4.set_ylabel('Prediction Confidence')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        # Add confidence statistics
        high_conf = np.mean(stage_confidence > 0.8) * 100
        med_conf = np.mean((stage_confidence > 0.6) & (stage_confidence <= 0.8)) * 100
        low_conf = np.mean(stage_confidence <= 0.6) * 100

        stats_text = f'High: {high_conf:.1f}%\nMedium: {med_conf:.1f}%\nLow: {low_conf:.1f}%'
        ax4.text(0.02, 0.98, stats_text, transform=ax4.transAxes,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()
        plt.savefig(self.output_dir / f'{save_name}.png', dpi=300, bbox_inches='tight')
        plt.show()

        return fig

    def create_sleep_stage_context_analysis(self, input_data, sample_labels=None, valid_mask=None, save_name="sleep_stage_context"):
        """Analyze sleep stages with 5-stage context window (5 before + current + 1 after)"""
        print("\n=== CREATING SLEEP STAGE CONTEXT ANALYSIS ===")

        with torch.no_grad():
            outputs = self.model(input_data)

        sleep_output = outputs[0]
        stage_probs = torch.softmax(sleep_output, dim=-1)
        predicted_stages = torch.argmax(stage_probs, dim=-1)[0].cpu().numpy()

        # Handle valid epochs
        if sample_labels is not None and len(sample_labels) > 0:
            sleep_labels = sample_labels[0].squeeze()
            valid_epochs = (sleep_labels != -1).cpu().numpy()
            predicted_stages = predicted_stages[valid_epochs]
            print(f"Analyzing {len(predicted_stages)} valid epochs")

        stage_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
        context_window = 2  
        after_window = 1    

        # Find examples of each sleep stage with sufficient context
        stage_contexts = {}

        for stage_idx, stage_name in enumerate(stage_names):
            print(f"Analyzing {stage_name} with context...")

            # Find epochs of this stage with sufficient context
            stage_epochs = np.where(predicted_stages == stage_idx)[0]

            # Filter epochs that have enough context (5 before, 1 after)
            valid_context_epochs = []
            for epoch in stage_epochs:
                if epoch >= context_window and epoch < len(predicted_stages) - after_window:
                    valid_context_epochs.append(epoch)

            if len(valid_context_epochs) == 0:
                print(f"  No {stage_name} epochs with sufficient context")
                continue

            # Take up to 3 examples
            np.random.shuffle(valid_context_epochs)
            sample_epochs = valid_context_epochs[:3]
            epoch_attributions = []

            for epoch_idx in sample_epochs:
                print(f"  Analyzing {stage_name} epoch {epoch_idx} with context")

                # Create wrapper for this specific epoch and stage
                def stage_context_wrapper(x):
                    outputs = self.model(x)
                    return outputs[0][:, epoch_idx, stage_idx]

                # Compute attributions
                ig = IntegratedGradients(stage_context_wrapper)
                attr = ig.attribute(input_data, baselines=torch.zeros_like(input_data), n_steps=30, internal_batch_size=1)

                # Extract context window from attribution
                # Convert epoch indices to sample indices
                context_start_epoch = epoch_idx - context_window
                context_end_epoch = epoch_idx + after_window + 1

                context_start_sample = context_start_epoch * 30 * self.sampling_rate
                context_end_sample = context_end_epoch * 30 * self.sampling_rate

                if context_end_sample <= attr.shape[-1]:
                    context_attr = attr[:, :, context_start_sample:context_end_sample].detach().cpu().numpy()
                    epoch_attributions.append({
                        'attribution': context_attr,
                        'context_stages': predicted_stages[context_start_epoch:context_end_epoch],
                        'target_epoch': epoch_idx,
                        'context_start': context_start_epoch
                    })

            if epoch_attributions:
                stage_contexts[stage_name] = epoch_attributions

        # Create visualization
        self._visualize_sleep_stage_contexts(stage_contexts, input_data, save_name)

        return stage_contexts

    def create_event_context_analysis(self, input_data, sample_labels=None, valid_mask=None, save_name="event_context"):
        """Analyze events with 30s before and 10s after context"""
        print("\n=== CREATING EVENT CONTEXT ANALYSIS ===")

        with torch.no_grad():
            outputs = self.model(input_data)

        # Determine valid time range for events
        valid_seconds = None
        if valid_mask is not None:
            valid_start, valid_end = valid_mask
            valid_seconds = valid_end // self.sampling_rate

        event_names = ['Arousal', 'Apnea', 'LM']
        event_contexts = {}

        for i, event_name in enumerate(event_names):
            if i + 1 >= len(outputs):
                continue

            print(f"Analyzing {event_name} events with context...")

            event_output = outputs[i + 1]
            if event_output.shape[-1] == 1:
                event_probs = torch.sigmoid(event_output).squeeze(-1)[0].cpu().numpy()
                event_predictions = event_probs > 0.5

                # Trim to valid data
                if valid_seconds is not None:
                    event_predictions = event_predictions[:valid_seconds]

                # Group consecutive events
                grouped_events = self.group_consecutive_events(event_predictions)
                print(f"  Found {len(grouped_events)} grouped {event_name} events")

                if len(grouped_events) == 0:
                    continue
                # Analyze up to 3 events with context
                np.random.shuffle(grouped_events)
                sample_events = grouped_events[:3]
                event_attributions = []

                for event_start, event_end in sample_events:
                    event_center = (event_start + event_end) // 2
                    event_duration = event_end - event_start + 1

                    print(f"  Analyzing {event_name} event: {event_duration}s duration at {event_center}s")

                    # Check if we have enough context (30s before, 10s after)
                    context_start = event_center - 30
                    context_end = event_center + 10

                    if context_start < 0 or context_end >= len(event_predictions):
                        print(f"    Skipping - insufficient context")
                        continue

                    # Create wrapper for the event center - average attribution over entire event
                    def event_context_wrapper(x):
                        outputs = self.model(x)
                        # Average attribution over the entire event duration
                        event_outputs = outputs[i + 1][:, event_start:event_end+1, 0]
                        return torch.mean(event_outputs, dim=1)

                    # Compute attributions
                    ig = IntegratedGradients(event_context_wrapper)
                    attr = ig.attribute(input_data, baselines=torch.zeros_like(input_data), n_steps=30, internal_batch_size=1)

                    # Extract context window from attribution
                    context_start_sample = context_start * self.sampling_rate
                    context_end_sample = context_end * self.sampling_rate

                    if context_end_sample <= attr.shape[-1]:
                        context_attr = attr[:, :, context_start_sample:context_end_sample].detach().cpu().numpy()
                        event_attributions.append({
                            'attribution': context_attr,
                            'event_start': event_start,
                            'event_end': event_end,
                            'event_center': event_center,
                            'context_start': context_start,
                            'context_end': context_end
                        })

                if event_attributions:
                    event_contexts[event_name] = event_attributions

        # Create visualization
        self._visualize_event_contexts(event_contexts, input_data, save_name)

        return event_contexts

    def create_architecture_activation_analysis(self, input_data, sample_labels=None, valid_mask=None, save_name="architecture_activations"):
        """Analyze model architecture activations for each task and class"""
        print("\n=== CREATING ARCHITECTURE ACTIVATION ANALYSIS ===")

        # Hook to capture intermediate activations
        activations = {}

        def get_activation(name):
            def hook(model, input, output):
                if isinstance(output, torch.Tensor):
                    activations[name] = output.detach().cpu().numpy()
                elif isinstance(output, (list, tuple)):
                    activations[name] = [o.detach().cpu().numpy() if isinstance(o, torch.Tensor) else o for o in output]
            return hook

        # Register hooks for different layers
        hooks = []
        layer_names = []

        # Try to hook common layer types
        for name, module in self.model.named_modules():
            if any(layer_type in str(type(module)).lower() for layer_type in ['conv', 'linear', 'lstm', 'gru', 'transformer']):
                if len(layer_names) < 10:  # Limit to 10 layers for visualization
                    hook = module.register_forward_hook(get_activation(name))
                    hooks.append(hook)
                    layer_names.append(name)

        print(f"Monitoring {len(layer_names)} layers: {layer_names}")

        # Get model outputs and activations
        with torch.no_grad():
            outputs = self.model(input_data)

        # Remove hooks
        for hook in hooks:
            hook.remove()

        # Analyze activations for each task
        task_activations = {}

        for task_idx, task_name in enumerate(self.task_names):
            if task_idx >= len(outputs):
                continue

            print(f"Analyzing {task_name} task activations...")

            task_output = outputs[task_idx]

            if task_output.shape[-1] > 1:
                # Multi-class task
                class_probs = torch.softmax(task_output, dim=-1)
                predicted_classes = torch.argmax(class_probs, dim=-1)[0].cpu().numpy()

                # Analyze activations for each class
                class_activations = {}
                n_classes = task_output.shape[-1]

                for class_idx in range(n_classes):
                    class_mask = predicted_classes == class_idx
                    if np.any(class_mask):
                        # Get representative time points for this class
                        class_times = np.where(class_mask)[0]
                        sample_times = class_times[:5] if len(class_times) > 5 else class_times

                        # Collect activations for this class
                        layer_stats = {}
                        for layer_name in layer_names:
                            if layer_name in activations:
                                layer_output = activations[layer_name]
                                if isinstance(layer_output, np.ndarray) and len(layer_output.shape) >= 2:
                                    # Average activation across sample times
                                    if len(layer_output.shape) == 3:  # [B, T, Features]
                                        if layer_output.shape[1] > max(sample_times):
                                            class_activations_layer = layer_output[0, sample_times, :]
                                            layer_stats[layer_name] = {
                                                'mean': np.mean(class_activations_layer, axis=0),
                                                'std': np.std(class_activations_layer, axis=0),
                                                'max': np.max(class_activations_layer, axis=0)
                                            }
                                    elif len(layer_output.shape) == 2:  # [B, Features]
                                        layer_stats[layer_name] = {
                                            'mean': np.mean(layer_output[0]),
                                            'std': np.std(layer_output[0]),
                                            'max': np.max(layer_output[0])
                                        }

                        class_activations[f'class_{class_idx}'] = layer_stats

                task_activations[task_name] = class_activations

            else:
                # Binary task
                probs = torch.sigmoid(task_output).squeeze(-1)[0].cpu().numpy()
                predictions = probs > 0.5

                # Analyze activations for positive predictions
                positive_times = np.where(predictions)[0]
                if len(positive_times) > 0:
                    sample_times = positive_times[:5] if len(positive_times) > 5 else positive_times

                    layer_stats = {}
                    for layer_name in layer_names:
                        if layer_name in activations:
                            layer_output = activations[layer_name]
                            if isinstance(layer_output, np.ndarray) and len(layer_output.shape) >= 2:
                                if len(layer_output.shape) == 3:  # [B, T, Features]
                                    if layer_output.shape[1] > max(sample_times):
                                        positive_activations = layer_output[0, sample_times, :]
                                        layer_stats[layer_name] = {
                                            'mean': np.mean(positive_activations, axis=0),
                                            'std': np.std(positive_activations, axis=0),
                                            'max': np.max(positive_activations, axis=0)
                                        }
                                elif len(layer_output.shape) == 2:  # [B, Features]
                                    layer_stats[layer_name] = {
                                        'mean': np.mean(layer_output[0]),
                                        'std': np.std(layer_output[0]),
                                        'max': np.max(layer_output[0])
                                    }

                    task_activations[task_name] = {'positive': layer_stats}

        # Create visualization
        self._visualize_architecture_activations(task_activations, save_name)

        return task_activations

    def _visualize_sleep_stage_contexts(self, stage_contexts, input_data, save_name):
        """Visualize sleep stage context analysis with biosignals"""
        if not stage_contexts:
            print("No stage contexts to visualize")
            return

        stage_colors = ['red', 'orange', 'lightblue', 'blue', 'green']
        stage_names = ['Wake', 'N1', 'N2', 'N3', 'REM']

        for stage_name, stage_data in stage_contexts.items():
            if not stage_data:
                continue

            # Create figure for this stage with biosignals
            fig = plt.figure(figsize=(24, 16))
            gs = fig.add_gridspec(4, 2, height_ratios=[1, 3, 1, 1], hspace=0.3, wspace=0.2)

            # Use first example for detailed visualization
            example = stage_data[0]
            context_start_epoch = example['context_start']
            target_epoch = example['target_epoch']
            attribution = example['attribution'][0]  # [C, T]
            context_stages = example['context_stages']

            # Extract context signal data
            context_start_sample = context_start_epoch * 30 * self.sampling_rate
            context_end_sample = (context_start_epoch + 7) * 30 * self.sampling_rate  # 7 epochs

            if context_end_sample <= input_data.shape[-1]:
                context_signals = input_data[0, :, context_start_sample:context_end_sample].cpu().numpy()

                # Time axis for context window
                context_duration = 7 * 30  # 7 epochs * 30 seconds
                time_axis = np.linspace(0, context_duration, context_signals.shape[1])

                # 1. Sleep stage context bar (top)
                ax_stages = fig.add_subplot(gs[0, :])
                epoch_positions = np.arange(7) * 30  # Epoch positions in seconds
                epoch_widths = [30] * 7
                colors = [stage_colors[stage] for stage in context_stages]

                bars = ax_stages.barh([0] * 7, epoch_widths, left=epoch_positions,
                                    color=colors, alpha=0.7, height=0.8)

                # Highlight target epoch
                target_pos_in_context = 5  # 5th epoch (0-indexed)
                bars[target_pos_in_context].set_edgecolor('black')
                bars[target_pos_in_context].set_linewidth(4)

                # Add stage labels
                for i, (pos, stage) in enumerate(zip(epoch_positions, context_stages)):
                    ax_stages.text(pos + 15, 0, stage_names[stage], ha='center', va='center',
                                 fontweight='bold', color='white' if stage in [2, 3] else 'black')

                ax_stages.set_xlim(0, context_duration)
                ax_stages.set_ylim(-0.5, 0.5)
                ax_stages.set_title(f'{stage_name} Stage Context - Target Epoch Highlighted', fontsize=16, fontweight='bold')
                ax_stages.set_xlabel('Time (seconds)')
                ax_stages.set_yticks([])

                # Add epoch markers
                for i in range(8):  # 7 epochs = 8 boundaries
                    ax_stages.axvline(x=i*30, color='black', linestyle='-', alpha=0.3)

                # 2. Biosignals with attribution overlay (main plot)
                ax_signals = fig.add_subplot(gs[1, :])

                # Plot each channel with attribution-based coloring
                y_offset = 0
                y_spacing = 3
                channel_positions = []

                for ch in range(min(context_signals.shape[0], len(self.channel_names))):
                    signal = context_signals[ch]
                    attr = np.abs(attribution[ch])

                    # Normalize signal for display
                    signal_norm = (signal - np.mean(signal)) / (np.std(signal) + 1e-8)
                    signal_display = signal_norm + y_offset

                    # Normalize attribution for color mapping
                    attr_norm = (attr - np.min(attr)) / (np.max(attr) - np.min(attr) + 1e-8)

                    # Create color-coded line based on attribution
                    points = np.array([time_axis, signal_display]).T.reshape(-1, 1, 2)
                    segments = np.concatenate([points[:-1], points[1:]], axis=1)

                    from matplotlib.collections import LineCollection
                    lc = LineCollection(segments, cmap='Reds', alpha=0.8)
                    lc.set_array(attr_norm[:-1])  # Use attribution for coloring
                    lc.set_linewidth(1.5)
                    line = ax_signals.add_collection(lc)

                    # Add baseline signal in gray for reference
                    ax_signals.plot(time_axis, signal_display, color='gray', alpha=0.3, linewidth=0.5)

                    # Channel label
                    ax_signals.text(-5, y_offset, self.channel_names[ch],
                                  ha='right', va='center', fontweight='bold')

                    channel_positions.append(y_offset)
                    y_offset += y_spacing

                # Highlight target epoch region
                target_start = 5 * 30  # 5th epoch start
                target_end = 6 * 30    # 5th epoch end
                ax_signals.axvspan(target_start, target_end, alpha=0.2, color='yellow',
                                 label=f'Target {stage_name} Epoch')

                # Add epoch boundaries
                for i in range(8):
                    ax_signals.axvline(x=i*30, color='black', linestyle='--', alpha=0.3)

                ax_signals.set_xlim(0, context_duration)
                ax_signals.set_ylim(-2, y_offset + 1)
                ax_signals.set_title('PSG Signals with Attribution Intensity (Red = High Attribution)',
                                   fontsize=14)
                ax_signals.set_xlabel('Time (seconds)')
                ax_signals.set_ylabel('PSG Channels')
                ax_signals.set_yticks(channel_positions)
                ax_signals.set_yticklabels([self.channel_names[i] for i in range(len(channel_positions))])
                ax_signals.legend()
                ax_signals.grid(True, alpha=0.3)

                # Add colorbar for attribution
                sm = plt.cm.ScalarMappable(cmap='Reds', norm=plt.Normalize(vmin=0, vmax=1))
                sm.set_array([])
                cbar = plt.colorbar(sm, ax=ax_signals, shrink=0.8)
                cbar.set_label('Attribution Intensity', rotation=270, labelpad=20)

                # 3. Channel attribution summary (bottom left)
                ax_attr = fig.add_subplot(gs[2, 0])

                # Average attribution per channel
                channel_attr_avg = [np.mean(np.abs(attribution[ch])) for ch in range(len(self.channel_names))]

                bars = ax_attr.barh(range(len(channel_attr_avg)), channel_attr_avg,
                                  color='skyblue', alpha=0.7)
                ax_attr.set_yticks(range(len(self.channel_names)))
                ax_attr.set_yticklabels(self.channel_names)
                ax_attr.set_xlabel('Average Attribution')
                ax_attr.set_title('Channel Importance')
                ax_attr.grid(True, alpha=0.3)

                # Highlight top contributing channels
                max_attr = max(channel_attr_avg)
                for i, (bar, attr_val) in enumerate(zip(bars, channel_attr_avg)):
                    if attr_val > 0.7 * max_attr:
                        bar.set_color('red')
                        bar.set_alpha(0.8)

                # 4. Temporal attribution pattern (bottom right)
                ax_temporal = fig.add_subplot(gs[2, 1])

                # Average attribution across all channels over time
                temporal_attr = np.mean(np.abs(attribution), axis=0)

                ax_temporal.plot(time_axis, temporal_attr, linewidth=2, color='purple')
                ax_temporal.fill_between(time_axis, 0, temporal_attr, alpha=0.3, color='purple')

                # Highlight target epoch
                ax_temporal.axvspan(target_start, target_end, alpha=0.3, color='yellow')

                # Add epoch boundaries
                for i in range(8):
                    ax_temporal.axvline(x=i*30, color='black', linestyle='--', alpha=0.3)

                ax_temporal.set_xlim(0, context_duration)
                ax_temporal.set_xlabel('Time (seconds)')
                ax_temporal.set_ylabel('Attribution')
                ax_temporal.set_title('Temporal Attribution Pattern')
                ax_temporal.grid(True, alpha=0.3)

                # 5. Statistics and interpretation (bottom)
                ax_stats = fig.add_subplot(gs[3, :])
                ax_stats.axis('off')

                # Calculate statistics
                target_attr = np.mean(temporal_attr[target_start*self.sampling_rate//100:(target_end*self.sampling_rate//100)])
                context_attr = np.mean(temporal_attr)
                peak_time = time_axis[np.argmax(temporal_attr)]

                # Top contributing channels
                top_channels = sorted(enumerate(channel_attr_avg), key=lambda x: x[1], reverse=True)[:3]
                top_channel_text = ", ".join([f"{self.channel_names[ch]} ({attr:.3f})" for ch, attr in top_channels])

                stats_text = f"""
                ANALYSIS SUMMARY:
                • Target {stage_name} Epoch Attribution: {target_attr:.4f}
                • Overall Context Attribution: {context_attr:.4f}
                • Peak Attribution Time: {peak_time:.1f}s
                • Top Contributing Channels: {top_channel_text}
                • Context: {' → '.join([stage_names[s] for s in context_stages])}
                """

                ax_stats.text(0.05, 0.5, stats_text, transform=ax_stats.transAxes,
                            fontsize=12, verticalalignment='center',
                            bbox=dict(boxstyle='round,pad=1', facecolor='lightblue', alpha=0.8))

                plt.suptitle(f'{stage_name} Sleep Stage - Context Analysis with Biosignals',
                           fontsize=18, fontweight='bold')

                # Save individual stage plot
                stage_save_name = f"{save_name}_{stage_name.lower()}"
                plt.savefig(self.output_dir / f'{stage_save_name}.png', dpi=300, bbox_inches='tight')
                plt.show()

                print(f"✅ {stage_name} context visualization completed")

    def _visualize_event_contexts(self, event_contexts, input_data, save_name):
        """Visualize event context analysis with biosignals"""
        if not event_contexts:
            print("No event contexts to visualize")
            return

        event_colors = ['red', 'blue', 'purple']

        for event_idx, (event_name, event_data) in enumerate(event_contexts.items()):
            if not event_data:
                continue

            # Create figure for this event type with biosignals
            fig = plt.figure(figsize=(24, 16))
            gs = fig.add_gridspec(4, 2, height_ratios=[0.5, 3, 1, 1], hspace=0.3, wspace=0.2)

            # Use first example for detailed visualization
            example = event_data[0]
            event_start = example['event_start']
            event_end = example['event_end']
            event_center = example['event_center']
            context_start = example['context_start']
            context_end = example['context_end']
            attribution = example['attribution'][0]  # [C, T]

            # Extract context signal data (30s before + 10s after)
            context_start_sample = context_start * self.sampling_rate
            context_end_sample = context_end * self.sampling_rate

            if context_end_sample <= input_data.shape[-1]:
                context_signals = input_data[0, :, context_start_sample:context_end_sample].cpu().numpy()

                # Time axis for context window (relative to event)
                context_duration = 40  # 30s before + 10s after
                time_axis = np.linspace(-30, 10, context_signals.shape[1])

                # 1. Event timeline (top)
                ax_timeline = fig.add_subplot(gs[0, :])

                # Show event period
                event_rel_start = event_start - context_start
                event_rel_end = event_end - context_start
                event_duration = event_end - event_start + 1

                # Background for context
                ax_timeline.barh([0], [40], left=[-30], color='lightgray', alpha=0.3, height=0.6)

                # Event period
                ax_timeline.barh([0], [event_duration], left=[event_rel_start],
                                color=event_colors[event_idx % len(event_colors)], alpha=0.8, height=0.8)

                ax_timeline.set_xlim(-30, 10)
                ax_timeline.set_ylim(-0.5, 0.5)
                ax_timeline.set_title(f'{event_name} Event Context - 30s Before + 10s After',
                                    fontsize=16, fontweight='bold')
                ax_timeline.set_xlabel('Time relative to event (seconds)')
                ax_timeline.set_yticks([])

                # Add markers
                ax_timeline.axvline(x=0, color='red', linestyle='--', linewidth=2, alpha=0.8, label='Event Start')
                ax_timeline.axvline(x=event_duration, color='red', linestyle=':', linewidth=2, alpha=0.8, label='Event End')
                ax_timeline.legend()

                # Add time grid
                for t in range(-30, 11, 10):
                    ax_timeline.axvline(x=t, color='black', linestyle='-', alpha=0.2)

                # 2. Biosignals with attribution overlay (main plot)
                ax_signals = fig.add_subplot(gs[1, :])

                # Plot each channel with attribution-based coloring
                y_offset = 0
                y_spacing = 3
                channel_positions = []

                for ch in range(min(context_signals.shape[0], len(self.channel_names))):
                    signal = context_signals[ch]
                    attr = np.abs(attribution[ch])

                    # Normalize signal for display
                    signal_norm = (signal - np.mean(signal)) / (np.std(signal) + 1e-8)
                    signal_display = signal_norm + y_offset

                    # Normalize attribution for color mapping
                    attr_norm = (attr - np.min(attr)) / (np.max(attr) - np.min(attr) + 1e-8)

                    # Create color-coded line based on attribution
                    points = np.array([time_axis, signal_display]).T.reshape(-1, 1, 2)
                    segments = np.concatenate([points[:-1], points[1:]], axis=1)

                    from matplotlib.collections import LineCollection
                    lc = LineCollection(segments, cmap='Reds', alpha=0.8)
                    lc.set_array(attr_norm[:-1])  # Use attribution for coloring
                    lc.set_linewidth(1.5)
                    line = ax_signals.add_collection(lc)

                    # Add baseline signal in gray for reference
                    ax_signals.plot(time_axis, signal_display, color='gray', alpha=0.3, linewidth=0.5)

                    # Channel label
                    ax_signals.text(-32, y_offset, self.channel_names[ch],
                                  ha='right', va='center', fontweight='bold')

                    channel_positions.append(y_offset)
                    y_offset += y_spacing

                # Highlight event period
                ax_signals.axvspan(event_rel_start, event_rel_end, alpha=0.3, color='yellow',
                                 label=f'{event_name} Event Period')

                # Add time markers
                ax_signals.axvline(x=0, color='red', linestyle='--', alpha=0.7, label='Event Start')
                for t in range(-30, 11, 10):
                    ax_signals.axvline(x=t, color='black', linestyle='-', alpha=0.2)

                ax_signals.set_xlim(-30, 10)
                ax_signals.set_ylim(-2, y_offset + 1)
                ax_signals.set_title('PSG Signals with Attribution Intensity (Red = High Attribution)',
                                   fontsize=14)
                ax_signals.set_xlabel('Time relative to event (seconds)')
                ax_signals.set_ylabel('PSG Channels')
                ax_signals.set_yticks(channel_positions)
                ax_signals.set_yticklabels([self.channel_names[i] for i in range(len(channel_positions))])
                ax_signals.legend()
                ax_signals.grid(True, alpha=0.3)

                # Add colorbar for attribution
                sm = plt.cm.ScalarMappable(cmap='Reds', norm=plt.Normalize(vmin=0, vmax=1))
                sm.set_array([])
                cbar = plt.colorbar(sm, ax=ax_signals, shrink=0.8)
                cbar.set_label('Attribution Intensity', rotation=270, labelpad=20)

                # 3. Channel attribution summary (bottom left)
                ax_attr = fig.add_subplot(gs[2, 0])

                # Average attribution per channel
                channel_attr_avg = [np.mean(np.abs(attribution[ch])) for ch in range(len(self.channel_names))]

                bars = ax_attr.barh(range(len(channel_attr_avg)), channel_attr_avg,
                                  color='skyblue', alpha=0.7)
                ax_attr.set_yticks(range(len(self.channel_names)))
                ax_attr.set_yticklabels(self.channel_names)
                ax_attr.set_xlabel('Average Attribution')
                ax_attr.set_title('Channel Importance')
                ax_attr.grid(True, alpha=0.3)

                # Highlight top contributing channels
                max_attr = max(channel_attr_avg)
                for i, (bar, attr_val) in enumerate(zip(bars, channel_attr_avg)):
                    if attr_val > 0.7 * max_attr:
                        bar.set_color('red')
                        bar.set_alpha(0.8)

                # 4. Temporal attribution pattern (bottom right)
                ax_temporal = fig.add_subplot(gs[2, 1])

                # Average attribution across all channels over time
                temporal_attr = np.mean(np.abs(attribution), axis=0)

                ax_temporal.plot(time_axis, temporal_attr, linewidth=2, color='purple')
                ax_temporal.fill_between(time_axis, 0, temporal_attr, alpha=0.3, color='purple')

                # Highlight event period
                ax_temporal.axvspan(event_rel_start, event_rel_end, alpha=0.3, color='yellow')
                ax_temporal.axvline(x=0, color='red', linestyle='--', alpha=0.7)

                # Add time grid
                for t in range(-30, 11, 10):
                    ax_temporal.axvline(x=t, color='black', linestyle='-', alpha=0.2)

                ax_temporal.set_xlim(-30, 10)
                ax_temporal.set_xlabel('Time relative to event (seconds)')
                ax_temporal.set_ylabel('Attribution')
                ax_temporal.set_title('Temporal Attribution Pattern')
                ax_temporal.grid(True, alpha=0.3)

                # 5. Statistics and interpretation (bottom)
                ax_stats = fig.add_subplot(gs[3, :])
                ax_stats.axis('off')

                # Calculate statistics
                event_attr = np.mean(temporal_attr[int((event_rel_start + 30) * self.sampling_rate / 100):
                                                  int((event_rel_end + 30) * self.sampling_rate / 100)])
                pre_event_attr = np.mean(temporal_attr[:int(30 * self.sampling_rate / 100)])
                post_event_attr = np.mean(temporal_attr[int(30 * self.sampling_rate / 100):])
                peak_time = time_axis[np.argmax(temporal_attr)]

                # Top contributing channels
                top_channels = sorted(enumerate(channel_attr_avg), key=lambda x: x[1], reverse=True)[:3]
                top_channel_text = ", ".join([f"{self.channel_names[ch]} ({attr:.3f})" for ch, attr in top_channels])

                # Clinical interpretation
                if event_name == 'Apnea':
                    interpretation = "Breathing pattern changes and airflow reduction drive detection"
                elif event_name == 'Arousal':
                    interpretation = "EEG and physiological changes indicate arousal response"
                elif event_name == 'LM':
                    interpretation = "EMG activation patterns indicate limb movement"
                else:
                    interpretation = "Model focuses on specific signal patterns for detection"

                stats_text = f"""
                ANALYSIS SUMMARY:
                • Event Duration: {event_duration}s
                • Event Attribution: {event_attr:.4f}
                • Pre-Event Attribution: {pre_event_attr:.4f}
                • Post-Event Attribution: {post_event_attr:.4f}
                • Peak Attribution Time: {peak_time:.1f}s
                • Top Contributing Channels: {top_channel_text}
                • Clinical Insight: {interpretation}
                """

                ax_stats.text(0.05, 0.5, stats_text, transform=ax_stats.transAxes,
                            fontsize=12, verticalalignment='center',
                            bbox=dict(boxstyle='round,pad=1', facecolor='lightgreen', alpha=0.8))

                plt.suptitle(f'{event_name} Event - Context Analysis with Biosignals',
                           fontsize=18, fontweight='bold')

                # Save individual event plot
                event_save_name = f"{save_name}_{event_name.lower()}_{event_idx}"
                plt.savefig(self.output_dir / f'{event_save_name}.png', dpi=300, bbox_inches='tight')
                plt.show()

                print(f"✅ {event_name} context visualization completed")

    def _visualize_architecture_activations(self, task_activations, save_name):
        """Visualize model architecture activations"""
        if not task_activations:
            print("No task activations to visualize")
            return

        # Create comprehensive visualization
        n_tasks = len(task_activations)
        fig, axes = plt.subplots(n_tasks, 2, figsize=(20, 6 * n_tasks))
        if n_tasks == 1:
            axes = axes.reshape(1, -1)

        task_colors = ['blue', 'red', 'green', 'purple']

        for task_idx, (task_name, task_data) in enumerate(task_activations.items()):
            if task_idx >= len(axes):
                break

            # Left plot: Layer activation heatmap across classes
            ax_left = axes[task_idx, 0]

            # Collect activation data for heatmap
            layer_names = []
            class_names = []
            activation_matrix = []

            for class_name, class_data in task_data.items():
                class_names.append(class_name)
                class_activations = []

                for layer_name, layer_stats in class_data.items():
                    if layer_name not in layer_names:
                        layer_names.append(layer_name)

                    # Use mean activation as representative value
                    if isinstance(layer_stats['mean'], np.ndarray):
                        activation_value = np.mean(layer_stats['mean'])
                    else:
                        activation_value = layer_stats['mean']

                    class_activations.append(activation_value)

                # Pad with zeros if some layers are missing for this class
                while len(class_activations) < len(layer_names):
                    class_activations.append(0)

                activation_matrix.append(class_activations[:len(layer_names)])

            if activation_matrix and layer_names:
                activation_matrix = np.array(activation_matrix)

                # Normalize for better visualization
                activation_matrix_norm = (activation_matrix - np.min(activation_matrix)) / (np.max(activation_matrix) - np.min(activation_matrix) + 1e-8)

                im = ax_left.imshow(activation_matrix_norm, aspect='auto', cmap='viridis', interpolation='nearest')
                ax_left.set_title(f'{task_name} - Layer Activations Across Classes')
                ax_left.set_ylabel('Classes/Conditions')
                ax_left.set_xlabel('Model Layers')

                # Set labels
                ax_left.set_yticks(range(len(class_names)))
                ax_left.set_yticklabels(class_names)
                ax_left.set_xticks(range(len(layer_names)))
                ax_left.set_xticklabels([name.split('.')[-1] for name in layer_names], rotation=45, ha='right')

                plt.colorbar(im, ax=ax_left, label='Normalized Activation')

                # Add activation values as text
                for i in range(len(class_names)):
                    for j in range(len(layer_names)):
                        text = ax_left.text(j, i, f'{activation_matrix_norm[i, j]:.2f}',
                                          ha="center", va="center", color="white" if activation_matrix_norm[i, j] > 0.5 else "black",
                                          fontsize=8)

            # Right plot: Activation statistics and patterns
            ax_right = axes[task_idx, 1]

            # Show activation patterns across layers for each class
            if layer_names and task_data:
                x_pos = np.arange(len(layer_names))
                width = 0.8 / len(task_data)  # Width of bars

                for class_idx, (class_name, class_data) in enumerate(task_data.items()):
                    class_means = []
                    class_stds = []

                    for layer_name in layer_names:
                        if layer_name in class_data:
                            layer_stats = class_data[layer_name]
                            if isinstance(layer_stats['mean'], np.ndarray):
                                mean_val = np.mean(layer_stats['mean'])
                                std_val = np.mean(layer_stats['std']) if 'std' in layer_stats else 0
                            else:
                                mean_val = layer_stats['mean']
                                std_val = layer_stats.get('std', 0)
                        else:
                            mean_val = 0
                            std_val = 0

                        class_means.append(mean_val)
                        class_stds.append(std_val)

                    # Plot bars for this class
                    offset = (class_idx - len(task_data)/2 + 0.5) * width
                    bars = ax_right.bar(x_pos + offset, class_means, width,
                                      label=class_name, alpha=0.7,
                                      color=task_colors[class_idx % len(task_colors)])

                    # Add error bars if we have std data
                    if any(std > 0 for std in class_stds):
                        ax_right.errorbar(x_pos + offset, class_means, yerr=class_stds,
                                        fmt='none', color='black', alpha=0.5, capsize=2)

                ax_right.set_title(f'{task_name} - Layer Activation Patterns')
                ax_right.set_xlabel('Model Layers')
                ax_right.set_ylabel('Activation Magnitude')
                ax_right.set_xticks(x_pos)
                ax_right.set_xticklabels([name.split('.')[-1] for name in layer_names], rotation=45, ha='right')
                ax_right.legend()
                ax_right.grid(True, alpha=0.3)

                # Add insights text
                insights = []
                if task_name == 'Sleep':
                    insights.append("Different sleep stages show\ndistinct activation patterns")
                    insights.append("Deep layers likely capture\ncomplex sleep transitions")
                elif task_name in ['Arousal', 'Apnea', 'LM']:
                    insights.append("Event detection shows\nspike in specific layers")
                    insights.append("Early layers detect\nbasic signal patterns")

                if insights:
                    insight_text = '\n\n'.join(insights)
                    ax_right.text(0.02, 0.98, insight_text, transform=ax_right.transAxes,
                                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8),
                                fontsize=9)

        plt.tight_layout()
        plt.savefig(self.output_dir / f'{save_name}.png', dpi=300, bbox_inches='tight')
        plt.show()
