import matplotlib.pyplot as plt
import torch
import numpy as np

class LossAnalyzer:
    def __init__(self, model, loss_function):
        self.model = model
        self.loss_function = loss_function
        
    def analyze_task_losses(self, dataloader, device):
        """Analyze individual task losses"""
        self.model.eval()
        task_losses = []
        total_samples = 0
        
        with torch.no_grad():
            for batch_x, batch_y in dataloader:
                batch_x = batch_x.to(device)
                batch_y = [y.to(device) for y in batch_y]
                
                predictions = self.model(batch_x)
                
                # Get individual task losses
                loss = self.loss_function(predictions, batch_y)
                
                # Access individual task losses from loss function stats
                if hasattr(self.loss_function, 'stats'):
                    batch_task_losses = [self.loss_function.stats[f'task{i}_loss'] 
                                       for i in range(len(predictions))]
                    task_losses.append(batch_task_losses)
                
                total_samples += batch_x.size(0)
        
        # Average task losses
        avg_task_losses = np.mean(task_losses, axis=0)
        return avg_task_losses
    
    def plot_loss_evolution(self, log_path):
        """Plot training loss evolution from logs"""
        # This would read from your training logs
        # Implementation depends on your logging format
        pass
    
    def analyze_gradient_flow(self, sample_batch, device):
        """Analyze gradient flow through the network"""
        self.model.train()
        
        batch_x, batch_y = sample_batch
        batch_x = batch_x.to(device)
        batch_y = [y.to(device) for y in batch_y]
        
        # Forward pass
        predictions = self.model(batch_x)
        loss = self.loss_function(predictions, batch_y)
        
        # Backward pass
        loss.backward()
        
        # Collect gradients
        gradients = {}
        for name, param in self.model.named_parameters():
            if param.grad is not None:
                gradients[name] = {
                    'mean': param.grad.mean().item(),
                    'std': param.grad.std().item(),
                    'norm': param.grad.norm().item()
                }
        
        return gradients