import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from scipy import signal
from scipy.stats import entropy
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
import plotly.graph_objects as go
from plotly.subplots import make_subplots

class PSGSignalAnalyzer:
    """Advanced PSG signal analysis and visualization"""
    
    def __init__(self, sampling_rate=100):
        self.sampling_rate = sampling_rate
        self.output_dir = Path("signal_analysis")
        self.output_dir.mkdir(exist_ok=True)
        
        # PSG channel information
        self.channel_names = [
            'EEG',
            'EOG', 'EMG Chin', 'EMG Leg',
            'Position', 'Snore', 'Chest Effort', 'Abdominal Effort', 'SpO2', 'Airflow'
        ]
        
        self.channel_groups = {
            'EEG': [0],  # Brain activity
            'EOG': [1],  # Eye movements
            'EMG': [2, 3],  # Muscle activity (<PERSON>, Leg)
            'Respiratory': [5, 6, 7, 9],  # Breathing signals (<PERSON>nore, Chest, Abdominal, Airflow)
            'Physiological': [4, 8]  # Position, SpO2
        }
        
        # Frequency bands for EEG analysis
        self.freq_bands = {
            'Delta': (0.5, 4),
            'Theta': (4, 8),
            'Alpha': (8, 13),
            'Beta': (13, 30),
            'Gamma': (30, 50)
        }
    
    def analyze_signal_quality(self, input_data, sample_labels=None, save_name="signal_quality"):
        """Comprehensive signal quality analysis"""
        print("Analyzing PSG signal quality...")

        # Determine valid data range
        valid_range = None
        if sample_labels is not None and len(sample_labels) > 0:
            sleep_labels = sample_labels[0].squeeze()
            valid_epochs = (sleep_labels != -1).cpu().numpy()
            valid_seconds = np.sum(valid_epochs) * 30  # 30 seconds per epoch
            valid_samples = valid_seconds * self.sampling_rate
            valid_range = (0, min(valid_samples, input_data.shape[-1]))
            print(f"Analyzing signal quality for valid range: {valid_range[1]/self.sampling_rate/3600:.1f} hours")
        else:
            print("⚠️ No labels provided - analyzing full signal (may include padding)")

        n_channels = min(input_data.shape[1], len(self.channel_names))
        
        # Create quality metrics
        quality_metrics = {}
        
        for ch in range(n_channels):
            channel_name = self.channel_names[ch]
            signal_data = input_data[0, ch].cpu().numpy()

            # Use only valid data range if available
            if valid_range is not None:
                signal_data = signal_data[valid_range[0]:valid_range[1]]
            
            # Basic statistics
            metrics = {
                'mean': np.mean(signal_data),
                'std': np.std(signal_data),
                'range': np.max(signal_data) - np.min(signal_data),
                'zero_percentage': np.mean(signal_data == 0) * 100,
                'saturation_percentage': np.mean(np.abs(signal_data) > np.percentile(np.abs(signal_data), 99)) * 100
            }
            
            # Signal-to-noise ratio estimation
            # Use high-frequency content as noise estimate
            freqs, psd = signal.welch(signal_data, fs=self.sampling_rate, nperseg=1024)
            signal_power = np.sum(psd[freqs < 30])  # Signal below 30 Hz
            noise_power = np.sum(psd[freqs > 30])   # Noise above 30 Hz
            snr = 10 * np.log10(signal_power / (noise_power + 1e-10))
            metrics['snr_db'] = snr
            
            # Entropy (signal complexity)
            hist, _ = np.histogram(signal_data, bins=50, density=True)
            metrics['entropy'] = entropy(hist + 1e-10)
            
            # Artifact detection
            # Sudden amplitude changes
            diff_signal = np.diff(signal_data)
            artifact_threshold = 5 * np.std(diff_signal)
            artifacts = np.sum(np.abs(diff_signal) > artifact_threshold)
            metrics['artifact_count'] = artifacts
            metrics['artifact_percentage'] = (artifacts / len(diff_signal)) * 100
            
            quality_metrics[channel_name] = metrics
        
        # Create visualization
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        # Convert to DataFrame for easier plotting
        df = pd.DataFrame(quality_metrics).T
        
        # 1. Signal-to-Noise Ratio
        ax = axes[0]
        bars = ax.bar(range(len(df)), df['snr_db'], color='skyblue', alpha=0.7)
        ax.set_title('Signal-to-Noise Ratio by Channel')
        ax.set_ylabel('SNR (dB)')
        ax.set_xticks(range(len(df)))
        ax.set_xticklabels(df.index, rotation=45, ha='right')
        ax.grid(True, alpha=0.3)
        
        # Color code by quality
        for i, (bar, snr) in enumerate(zip(bars, df['snr_db'])):
            if snr > 20:
                bar.set_color('green')
            elif snr > 10:
                bar.set_color('orange')
            else:
                bar.set_color('red')
        
        # 2. Signal Entropy (Complexity)
        ax = axes[1]
        ax.bar(range(len(df)), df['entropy'], color='lightcoral', alpha=0.7)
        ax.set_title('Signal Entropy (Complexity)')
        ax.set_ylabel('Entropy')
        ax.set_xticks(range(len(df)))
        ax.set_xticklabels(df.index, rotation=45, ha='right')
        ax.grid(True, alpha=0.3)
        
        # 3. Artifact Percentage
        ax = axes[2]
        bars = ax.bar(range(len(df)), df['artifact_percentage'], color='salmon', alpha=0.7)
        ax.set_title('Artifact Percentage')
        ax.set_ylabel('Artifacts (%)')
        ax.set_xticks(range(len(df)))
        ax.set_xticklabels(df.index, rotation=45, ha='right')
        ax.grid(True, alpha=0.3)
        
        # Highlight high artifact channels
        for i, (bar, artifact_pct) in enumerate(zip(bars, df['artifact_percentage'])):
            if artifact_pct > 5:
                bar.set_color('red')
            elif artifact_pct > 2:
                bar.set_color('orange')
        
        # 4. Zero Values Percentage
        ax = axes[3]
        ax.bar(range(len(df)), df['zero_percentage'], color='lightblue', alpha=0.7)
        ax.set_title('Zero Values Percentage')
        ax.set_ylabel('Zero Values (%)')
        ax.set_xticks(range(len(df)))
        ax.set_xticklabels(df.index, rotation=45, ha='right')
        ax.grid(True, alpha=0.3)
        
        # 5. Signal Range
        ax = axes[4]
        ax.bar(range(len(df)), df['range'], color='lightgreen', alpha=0.7)
        ax.set_title('Signal Range')
        ax.set_ylabel('Range')
        ax.set_xticks(range(len(df)))
        ax.set_xticklabels(df.index, rotation=45, ha='right')
        ax.grid(True, alpha=0.3)
        
        # 6. Overall Quality Score
        ax = axes[5]
        
        # Calculate composite quality score
        quality_scores = []
        for ch_name in df.index:
            score = 0
            metrics = df.loc[ch_name]
            
            # SNR contribution (0-40 points)
            snr_score = min(40, max(0, metrics['snr_db'] * 2))
            score += snr_score
            
            # Artifact contribution (0-30 points, inverted)
            artifact_score = max(0, 30 - metrics['artifact_percentage'] * 6)
            score += artifact_score
            
            # Zero values contribution (0-20 points, inverted)
            zero_score = max(0, 20 - metrics['zero_percentage'] * 2)
            score += zero_score
            
            # Entropy contribution (0-10 points)
            entropy_score = min(10, metrics['entropy'] * 2)
            score += entropy_score
            
            quality_scores.append(score)
        
        bars = ax.bar(range(len(df)), quality_scores, alpha=0.7)
        ax.set_title('Overall Quality Score')
        ax.set_ylabel('Quality Score (0-100)')
        ax.set_xticks(range(len(df)))
        ax.set_xticklabels(df.index, rotation=45, ha='right')
        ax.grid(True, alpha=0.3)
        
        # Color code by quality
        for i, (bar, score) in enumerate(zip(bars, quality_scores)):
            if score > 80:
                bar.set_color('green')
            elif score > 60:
                bar.set_color('orange')
            else:
                bar.set_color('red')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / f'{save_name}.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return fig, quality_metrics
    
    def create_frequency_analysis(self, input_data, sample_labels=None, save_name="frequency_analysis"):
        """Detailed frequency domain analysis"""
        print("Creating frequency domain analysis...")

        # Determine valid data range
        valid_range = None
        if sample_labels is not None and len(sample_labels) > 0:
            sleep_labels = sample_labels[0].squeeze()
            valid_epochs = (sleep_labels != -1).cpu().numpy()
            valid_seconds = np.sum(valid_epochs) * 30
            valid_samples = valid_seconds * self.sampling_rate
            valid_range = (0, min(valid_samples, input_data.shape[-1]))
            print(f"Frequency analysis for valid range: {valid_range[1]/self.sampling_rate/3600:.1f} hours")
        
        # Focus on EEG channels for frequency analysis
        eeg_channels = self.channel_groups['EEG']
        
        fig, axes = plt.subplots(3, 2, figsize=(16, 12))
        
        # 1. Power Spectral Density for all EEG channels
        ax = axes[0, 0]
        colors = plt.cm.tab10(np.linspace(0, 1, len(eeg_channels)))
        
        for i, ch in enumerate(eeg_channels):
            if ch < input_data.shape[1]:
                signal_data = input_data[0, ch].cpu().numpy()

                # Use only valid data range if available
                if valid_range is not None:
                    signal_data = signal_data[valid_range[0]:valid_range[1]]

                freqs, psd = signal.welch(signal_data, fs=self.sampling_rate, nperseg=2048)
                ax.semilogy(freqs, psd, color=colors[i], label=self.channel_names[ch], alpha=0.8)
        
        ax.set_xlim(0, 50)
        ax.set_xlabel('Frequency (Hz)')
        ax.set_ylabel('Power Spectral Density')
        ax.set_title('EEG Power Spectral Density')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 2. Frequency band power analysis
        ax = axes[0, 1]
        
        band_powers = {band: [] for band in self.freq_bands.keys()}
        channel_labels = []
        
        for ch in eeg_channels:
            if ch < input_data.shape[1]:
                signal_data = input_data[0, ch].cpu().numpy()
                freqs, psd = signal.welch(signal_data, fs=self.sampling_rate, nperseg=2048)
                
                for band_name, (low, high) in self.freq_bands.items():
                    band_mask = (freqs >= low) & (freqs <= high)
                    band_power = np.sum(psd[band_mask])
                    band_powers[band_name].append(band_power)
                
                channel_labels.append(self.channel_names[ch])
        
        # Create stacked bar chart
        bottom = np.zeros(len(channel_labels))
        colors = ['red', 'orange', 'green', 'blue', 'purple']
        
        for i, (band_name, powers) in enumerate(band_powers.items()):
            ax.bar(channel_labels, powers, bottom=bottom, label=band_name, 
                  color=colors[i], alpha=0.7)
            bottom += powers
        
        ax.set_title('Frequency Band Power Distribution')
        ax.set_ylabel('Power')
        ax.legend()
        ax.tick_params(axis='x', rotation=45)
        
        # 3. Spectrogram for central EEG channel
        if len(eeg_channels) > 2:
            central_ch = eeg_channels[2]  # C3-M2
            if central_ch < input_data.shape[1]:
                ax = axes[1, :]
                signal_data = input_data[0, central_ch].cpu().numpy()
                
                # Downsample for visualization
                downsample_factor = 10
                signal_ds = signal_data[::downsample_factor]
                fs_ds = self.sampling_rate // downsample_factor
                
                f, t, Sxx = signal.spectrogram(signal_ds, fs_ds, nperseg=256, noverlap=128)
                
                # Plot spectrogram
                ax = plt.subplot(3, 2, (3, 4))  # Span two columns
                im = ax.pcolormesh(t/3600, f, 10*np.log10(Sxx), shading='gouraud', cmap='viridis')
                ax.set_ylabel('Frequency (Hz)')
                ax.set_xlabel('Time (hours)')
                ax.set_title(f'Spectrogram - {self.channel_names[central_ch]}')
                ax.set_ylim(0, 30)
                plt.colorbar(im, ax=ax, label='Power (dB)')
        
        # 4. Cross-channel coherence
        ax = axes[2, 0]
        
        if len(eeg_channels) >= 2:
            ch1, ch2 = eeg_channels[0], eeg_channels[1]  # F3-M2 and F4-M1
            if ch1 < input_data.shape[1] and ch2 < input_data.shape[1]:
                signal1 = input_data[0, ch1].cpu().numpy()
                signal2 = input_data[0, ch2].cpu().numpy()
                
                freqs, coherence = signal.coherence(signal1, signal2, fs=self.sampling_rate, nperseg=1024)
                
                ax.plot(freqs, coherence, linewidth=2)
                ax.set_xlim(0, 30)
                ax.set_xlabel('Frequency (Hz)')
                ax.set_ylabel('Coherence')
                ax.set_title(f'Coherence: {self.channel_names[ch1]} vs {self.channel_names[ch2]}')
                ax.grid(True, alpha=0.3)
                
                # Highlight frequency bands
                for band_name, (low, high) in self.freq_bands.items():
                    if high <= 30:
                        ax.axvspan(low, high, alpha=0.2, label=band_name)
                ax.legend()
        
        # 5. Respiratory signal analysis
        ax = axes[2, 1]
        
        respiratory_channels = [ch for ch in self.channel_groups['Respiratory'] if ch < input_data.shape[1]]
        if respiratory_channels:
            # Use airflow channel if available
            airflow_ch = respiratory_channels[-1] if len(respiratory_channels) > 0 else respiratory_channels[0]
            resp_signal = input_data[0, airflow_ch].cpu().numpy()
            
            # Estimate breathing rate
            # Filter signal to breathing frequency range (0.1-0.5 Hz)
            sos = signal.butter(4, [0.1, 0.5], btype='band', fs=self.sampling_rate, output='sos')
            filtered_resp = signal.sosfilt(sos, resp_signal)
            
            # Find peaks to estimate breathing rate
            peaks, _ = signal.find_peaks(filtered_resp, distance=self.sampling_rate)  # At least 1 second apart
            
            if len(peaks) > 1:
                breathing_intervals = np.diff(peaks) / self.sampling_rate  # Seconds
                breathing_rate = 60 / np.mean(breathing_intervals)  # Breaths per minute
                
                # Plot breathing rate over time
                time_windows = np.arange(0, len(resp_signal), 30 * self.sampling_rate)  # 30-second windows
                breathing_rates = []
                
                for i in range(len(time_windows) - 1):
                    start_idx = time_windows[i]
                    end_idx = time_windows[i + 1]
                    window_peaks = peaks[(peaks >= start_idx) & (peaks < end_idx)]
                    
                    if len(window_peaks) > 1:
                        window_intervals = np.diff(window_peaks) / self.sampling_rate
                        window_rate = 60 / np.mean(window_intervals)
                        breathing_rates.append(window_rate)
                    else:
                        breathing_rates.append(np.nan)
                
                time_hours = time_windows[:-1] / (self.sampling_rate * 3600)
                ax.plot(time_hours, breathing_rates, linewidth=2, marker='o', markersize=3)
                ax.set_xlabel('Time (hours)')
                ax.set_ylabel('Breathing Rate (breaths/min)')
                ax.set_title('Estimated Breathing Rate')
                ax.grid(True, alpha=0.3)
                
                # Add normal range
                ax.axhline(y=12, color='green', linestyle='--', alpha=0.7, label='Normal range')
                ax.axhline(y=20, color='green', linestyle='--', alpha=0.7)
                ax.fill_between(time_hours, 12, 20, alpha=0.2, color='green')
                ax.legend()
        
        plt.tight_layout()
        plt.savefig(self.output_dir / f'{save_name}.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return fig

    def create_time_domain_analysis(self, input_data, sample_labels=None, save_name="time_domain_analysis"):
        """Detailed time domain analysis of PSG signals"""
        print("Creating time domain analysis...")

        # Determine valid data range
        valid_range = None
        if sample_labels is not None and len(sample_labels) > 0:
            sleep_labels = sample_labels[0].squeeze()
            valid_epochs = (sleep_labels != -1).cpu().numpy()
            valid_seconds = np.sum(valid_epochs) * 30
            valid_samples = valid_seconds * self.sampling_rate
            valid_range = (0, min(valid_samples, input_data.shape[-1]))
            print(f"Time domain analysis for valid range: {valid_range[1]/self.sampling_rate/3600:.1f} hours")

        fig, axes = plt.subplots(3, 2, figsize=(16, 12))

        # 1. Signal amplitude distribution
        ax = axes[0, 0]

        for group_name, channels in self.channel_groups.items():
            if not channels:
                continue

            # Combine signals from this group
            group_signals = []
            for ch in channels:
                if ch < input_data.shape[1]:
                    signal_data = input_data[0, ch].cpu().numpy()

                    # Use only valid data range if available
                    if valid_range is not None:
                        signal_data = signal_data[valid_range[0]:valid_range[1]]

                    group_signals.extend(signal_data[::100])  # Downsample for speed

            if group_signals:
                ax.hist(group_signals, bins=50, alpha=0.6, label=group_name, density=True)

        ax.set_xlabel('Signal Amplitude')
        ax.set_ylabel('Density')
        ax.set_title('Signal Amplitude Distributions by Channel Group')
        ax.legend()
        ax.grid(True, alpha=0.3)

        # 2. Signal variability over time
        ax = axes[0, 1]

        # Calculate rolling standard deviation
        window_size = 30 * self.sampling_rate  # 30-second windows
        time_points = []
        variabilities = {}

        for group_name, channels in self.channel_groups.items():
            if not channels:
                continue

            group_variability = []
            for ch in channels:
                if ch < input_data.shape[1]:
                    signal_data = input_data[0, ch].cpu().numpy()

                    # Calculate rolling std
                    rolling_std = []
                    for i in range(0, len(signal_data) - window_size, window_size):
                        window = signal_data[i:i + window_size]
                        rolling_std.append(np.std(window))

                    if not group_variability:
                        group_variability = rolling_std
                        time_points = np.arange(len(rolling_std)) * 0.5 / 60  # Hours
                    else:
                        # Average with existing
                        min_len = min(len(group_variability), len(rolling_std))
                        group_variability = [(a + b) / 2 for a, b in zip(group_variability[:min_len], rolling_std[:min_len])]

            if group_variability:
                variabilities[group_name] = group_variability

        for group_name, variability in variabilities.items():
            ax.plot(time_points[:len(variability)], variability, label=group_name, linewidth=2)

        ax.set_xlabel('Time (hours)')
        ax.set_ylabel('Signal Variability (std)')
        ax.set_title('Signal Variability Over Time')
        ax.legend()
        ax.grid(True, alpha=0.3)

        # 3. Cross-correlation analysis
        ax = axes[1, 0]

        # Analyze correlation between EEG channels
        eeg_channels = self.channel_groups['EEG']
        if len(eeg_channels) >= 2:
            correlations = []
            channel_pairs = []

            for i in range(len(eeg_channels)):
                for j in range(i + 1, len(eeg_channels)):
                    ch1, ch2 = eeg_channels[i], eeg_channels[j]
                    if ch1 < input_data.shape[1] and ch2 < input_data.shape[1]:
                        signal1 = input_data[0, ch1].cpu().numpy()[::10]  # Downsample
                        signal2 = input_data[0, ch2].cpu().numpy()[::10]

                        # Calculate cross-correlation
                        correlation = np.corrcoef(signal1, signal2)[0, 1]
                        correlations.append(correlation)
                        channel_pairs.append(f'{self.channel_names[ch1][:6]}-{self.channel_names[ch2][:6]}')

            if correlations:
                bars = ax.bar(range(len(correlations)), correlations, alpha=0.7)
                ax.set_xlabel('Channel Pairs')
                ax.set_ylabel('Correlation Coefficient')
                ax.set_title('EEG Channel Cross-Correlations')
                ax.set_xticks(range(len(channel_pairs)))
                ax.set_xticklabels(channel_pairs, rotation=45, ha='right')
                ax.grid(True, alpha=0.3)

                # Color code correlations
                for bar, corr in zip(bars, correlations):
                    if corr > 0.7:
                        bar.set_color('green')
                    elif corr > 0.3:
                        bar.set_color('orange')
                    else:
                        bar.set_color('red')

        # 4. EMG activity analysis
        ax = axes[1, 1]

        emg_channels = [ch for ch in self.channel_groups['EMG'] if ch < input_data.shape[1]]
        if emg_channels:
            # Analyze EMG activity patterns
            emg_activities = {}

            for ch in emg_channels:
                emg_signal = input_data[0, ch].cpu().numpy()

                # Calculate EMG activity (RMS in windows)
                window_size = 30 * self.sampling_rate  # 30-second windows
                rms_values = []

                for i in range(0, len(emg_signal) - window_size, window_size):
                    window = emg_signal[i:i + window_size]
                    rms = np.sqrt(np.mean(window**2))
                    rms_values.append(rms)

                time_windows = np.arange(len(rms_values)) * 0.5 / 60  # Hours
                emg_activities[self.channel_names[ch]] = (time_windows, rms_values)

            # Plot EMG activities
            for ch_name, (times, activities) in emg_activities.items():
                ax.plot(times, activities, label=ch_name, linewidth=2)

            ax.set_xlabel('Time (hours)')
            ax.set_ylabel('EMG Activity (RMS)')
            ax.set_title('EMG Activity Patterns')
            ax.legend()
            ax.grid(True, alpha=0.3)

        # 5. Signal stationarity analysis
        ax = axes[2, 0]

        # Use central EEG channel for stationarity analysis
        eeg_channels = self.channel_groups['EEG']
        if len(eeg_channels) > 2:
            central_ch = eeg_channels[2]
            if central_ch < input_data.shape[1]:
                signal_data = input_data[0, central_ch].cpu().numpy()

                # Divide signal into segments and analyze variance
                n_segments = 20
                segment_length = len(signal_data) // n_segments
                segment_variances = []
                segment_means = []

                for i in range(n_segments):
                    start_idx = i * segment_length
                    end_idx = (i + 1) * segment_length
                    segment = signal_data[start_idx:end_idx]

                    segment_variances.append(np.var(segment))
                    segment_means.append(np.mean(segment))

                time_segments = np.arange(n_segments) * segment_length / (self.sampling_rate * 3600)

                ax2 = ax.twinx()
                line1 = ax.plot(time_segments, segment_variances, 'b-', label='Variance', linewidth=2)
                line2 = ax2.plot(time_segments, segment_means, 'r-', label='Mean', linewidth=2)

                ax.set_xlabel('Time (hours)')
                ax.set_ylabel('Variance', color='b')
                ax2.set_ylabel('Mean', color='r')
                ax.set_title(f'Signal Stationarity - {self.channel_names[central_ch]}')

                # Combine legends
                lines = line1 + line2
                labels = [l.get_label() for l in lines]
                ax.legend(lines, labels, loc='upper right')

                ax.grid(True, alpha=0.3)

        # 6. Respiratory pattern analysis
        ax = axes[2, 1]

        respiratory_channels = [ch for ch in self.channel_groups['Respiratory'] if ch < input_data.shape[1]]
        if respiratory_channels:
            # Use chest effort channel
            chest_ch = respiratory_channels[0] if len(respiratory_channels) > 0 else respiratory_channels[0]
            resp_signal = input_data[0, chest_ch].cpu().numpy()

            # Analyze respiratory patterns
            # Filter to respiratory frequency range
            sos = signal.butter(4, [0.1, 1.0], btype='band', fs=self.sampling_rate, output='sos')
            filtered_resp = signal.sosfilt(sos, resp_signal)

            # Calculate respiratory effort variability
            window_size = 60 * self.sampling_rate  # 1-minute windows
            effort_variability = []
            time_windows = []

            for i in range(0, len(filtered_resp) - window_size, window_size):
                window = filtered_resp[i:i + window_size]
                variability = np.std(window)
                effort_variability.append(variability)
                time_windows.append(i / (self.sampling_rate * 3600))

            ax.plot(time_windows, effort_variability, linewidth=2, color='blue')
            ax.set_xlabel('Time (hours)')
            ax.set_ylabel('Respiratory Effort Variability')
            ax.set_title('Respiratory Effort Patterns')
            ax.grid(True, alpha=0.3)

            # Highlight periods of high variability (potential respiratory events)
            high_variability_threshold = np.percentile(effort_variability, 90)
            high_var_mask = np.array(effort_variability) > high_variability_threshold
            if np.any(high_var_mask):
                high_var_times = np.array(time_windows)[high_var_mask]
                high_var_values = np.array(effort_variability)[high_var_mask]
                ax.scatter(high_var_times, high_var_values, color='red', s=30,
                          alpha=0.7, label='High Variability')
                ax.legend()

        plt.tight_layout()
        plt.savefig(self.output_dir / f'{save_name}.png', dpi=300, bbox_inches='tight')
        plt.show()

        return fig
