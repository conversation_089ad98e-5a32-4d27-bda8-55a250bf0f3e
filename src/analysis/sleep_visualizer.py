import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import pandas as pd
from scipy import signal
from scipy.stats import pearsonr
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px

class SleepVisualizationSuite:
    """Comprehensive sleep analysis visualization suite"""
    
    def __init__(self, model, device, sampling_rate=100):
        self.model = model
        self.device = device
        self.sampling_rate = sampling_rate
        self.output_dir = Path("sleep_visualizations")
        self.output_dir.mkdir(exist_ok=True)
        
        # PSG channel information
        self.channel_names = [
            'EEG',
            'EOG', 'EMG Chin', 'EMG Leg',
            'Position', 'Snore', 'Chest Effort', 'Abdominal Effort', 'SpO2', 'Airflow'
        ]
        
        self.stage_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
        self.stage_colors = ['#FF6B6B', '#FFA726', '#42A5F5', '#1E88E5', '#66BB6A']
        
    def create_interactive_hypnogram(self, input_data, sample_labels=None, save_name="interactive_hypnogram"):
        """Create interactive hypnogram with Plotly"""
        print("Creating interactive hypnogram...")

        with torch.no_grad():
            outputs = self.model(input_data)

        # Extract sleep stages and confidence
        sleep_output = outputs[0]
        stage_probs = torch.softmax(sleep_output, dim=-1)
        predicted_stages = torch.argmax(stage_probs, dim=-1)[0].cpu().numpy()
        stage_confidence = torch.max(stage_probs, dim=-1)[0][0].cpu().numpy()

        # Handle valid data range
        if sample_labels is not None and len(sample_labels) > 0:
            sleep_labels = sample_labels[0].squeeze()
            valid_epochs = (sleep_labels != -1).cpu().numpy()
            print(f"Valid epochs for interactive hypnogram: {np.sum(valid_epochs)}/{len(valid_epochs)}")

            # Only use valid epochs
            predicted_stages = predicted_stages[valid_epochs]
            stage_confidence = stage_confidence[valid_epochs]
        else:
            print("⚠️ No labels provided - using all epochs (may include padding)")
        
        # Extract events
        events_data = {}
        event_names = ['Arousal', 'Apnea', 'LM']

        # Determine valid time range for events
        valid_seconds = None
        if sample_labels is not None and len(sample_labels) > 0:
            sleep_labels = sample_labels[0].squeeze()
            valid_epochs = (sleep_labels != -1).cpu().numpy()
            valid_seconds = np.sum(valid_epochs) * 30  # 30 seconds per epoch
            print(f"Valid seconds for events: {valid_seconds}")

        for i, event_name in enumerate(event_names):
            if i + 1 < len(outputs):
                event_output = outputs[i + 1]
                if event_output.shape[-1] == 1:
                    event_probs = torch.sigmoid(event_output).squeeze(-1)[0].cpu().numpy()
                    event_predictions = event_probs > 0.5

                    # Trim to valid data if available
                    if valid_seconds is not None:
                        event_predictions = event_predictions[:valid_seconds]

                    events_data[event_name] = event_predictions
        
        # Create time axes
        epoch_times = np.arange(len(predicted_stages)) * 0.5 / 60  # Hours
        event_times = np.arange(len(list(events_data.values())[0])) / 3600  # Hours
        
        # Create subplots with mixed types including biosignals
        fig = make_subplots(
            rows=6, cols=2,
            subplot_titles=['Sleep Stages', 'Model Confidence', 'Events', 'PSG Signals', 'Sleep Architecture', 'Event Rates'],
            vertical_spacing=0.06,
            horizontal_spacing=0.1,
            specs=[[{"colspan": 2}, None],
                   [{"colspan": 2}, None],
                   [{"colspan": 2}, None],
                   [{"colspan": 2}, None],
                   [{"type": "domain"}, {"type": "xy"}],
                   [{"colspan": 2}, None]],
            row_heights=[0.15, 0.15, 0.15, 0.25, 0.2, 0.1]
        )
        
        # 1. Sleep stages with confidence-based opacity
        for stage_idx, (stage_name, color) in enumerate(zip(self.stage_names, self.stage_colors)):
            stage_mask = predicted_stages == stage_idx
            if np.any(stage_mask):
                fig.add_trace(
                    go.Scatter(
                        x=epoch_times[stage_mask],
                        y=[stage_idx] * np.sum(stage_mask),
                        mode='markers',
                        marker=dict(
                            color=color,
                            size=8,
                            opacity=0.7
                        ),
                        name=stage_name,
                        hovertemplate=f'<b>{stage_name}</b><br>Time: %{{x:.1f}} hours<br>Confidence: %{{customdata:.2f}}<extra></extra>',
                        customdata=stage_confidence[stage_mask]
                    ),
                    row=1, col=1
                )
        
        # 2. Model confidence
        fig.add_trace(
            go.Scatter(
                x=epoch_times,
                y=stage_confidence,
                mode='lines',
                line=dict(color='purple', width=2),
                name='Confidence',
                hovertemplate='Time: %{x:.1f} hours<br>Confidence: %{y:.2f}<extra></extra>'
            ),
            row=2, col=1
        )
        
        # Add confidence thresholds
        for threshold, color, label in [(0.8, 'green', 'High'), (0.6, 'orange', 'Medium')]:
            fig.add_hline(y=threshold, line_dash="dash", line_color=color, 
                         annotation_text=f"{label} ({threshold})", row=2, col=1)
        
        # 3. Events
        event_colors = ['red', 'blue', 'purple']
        y_positions = [2, 1, 0]
        
        for (event_name, event_data), color, y_pos in zip(events_data.items(), event_colors, y_positions):
            event_indices = np.where(event_data)[0]
            if len(event_indices) > 0:
                event_hours = event_indices / 3600
                fig.add_trace(
                    go.Scatter(
                        x=event_hours,
                        y=[y_pos] * len(event_hours),
                        mode='markers',
                        marker=dict(color=color, size=6),
                        name=event_name,
                        hovertemplate=f'<b>{event_name}</b><br>Time: %{{x:.1f}} hours<extra></extra>'
                    ),
                    row=3, col=1
                )

        # 4. PSG Signals
        # Downsample signals for interactive visualization
        downsample_factor = max(1, input_data.shape[-1] // 10000)  # Show ~10k points max
        signal_times = np.arange(0, input_data.shape[-1], downsample_factor) / (self.sampling_rate * 3600)  # Hours

        # Trim to valid data if available
        if valid_seconds is not None:
            max_samples = valid_seconds * self.sampling_rate
            signal_times = signal_times[signal_times <= max_samples / (self.sampling_rate * 3600)]

        # Select key channels for display (avoid overcrowding)
        key_channels = [0, 1, 2, 9]  # EEG, EOG, EMG Chin, Airflow
        channel_colors = ['blue', 'green', 'red', 'orange']

        for i, ch in enumerate(key_channels):
            if ch < input_data.shape[1]:
                signal_data = input_data[0, ch, ::downsample_factor].cpu().numpy()

                # Trim to valid data
                if len(signal_data) > len(signal_times):
                    signal_data = signal_data[:len(signal_times)]
                elif len(signal_times) > len(signal_data):
                    signal_times = signal_times[:len(signal_data)]

                # Normalize and offset for display
                signal_norm = (signal_data - np.mean(signal_data)) / (np.std(signal_data) + 1e-8)
                signal_display = signal_norm + i * 3  # Offset each channel

                fig.add_trace(
                    go.Scatter(
                        x=signal_times,
                        y=signal_display,
                        mode='lines',
                        line=dict(color=channel_colors[i], width=1),
                        name=self.channel_names[ch],
                        hovertemplate=f'<b>{self.channel_names[ch]}</b><br>Time: %{{x:.2f}} hours<br>Value: %{{y:.2f}}<extra></extra>',
                        opacity=0.8
                    ),
                    row=4, col=1
                )

        # 5. Sleep architecture pie chart
        stage_counts = [np.sum(predicted_stages == i) for i in range(5)]
        stage_percentages = [count / len(predicted_stages) * 100 for count in stage_counts]

        fig.add_trace(
            go.Pie(
                labels=self.stage_names,
                values=stage_percentages,
                marker_colors=self.stage_colors,
                name="Sleep Architecture",
                hovertemplate='<b>%{label}</b><br>%{value:.1f}%<br>%{customdata:.0f} epochs<extra></extra>',
                customdata=stage_counts
            ),
            row=5, col=1
        )

        # 6. Event rates bar chart
        event_rates = {}
        total_hours = len(list(events_data.values())[0]) / 3600

        for event_name, event_data in events_data.items():
            total_events = np.sum(event_data)
            rate_per_hour = total_events / total_hours
            event_rates[event_name] = rate_per_hour

        fig.add_trace(
            go.Bar(
                x=list(event_rates.keys()),
                y=list(event_rates.values()),
                marker_color=['red', 'blue', 'purple'],
                name="Event Rates",
                hovertemplate='<b>%{x}</b><br>Rate: %{y:.1f}/hour<extra></extra>'
            ),
            row=5, col=2
        )
        
        # Update layout
        fig.update_layout(
            height=1400,  # Increased height for additional signals
            title_text="Interactive Sleep Analysis Dashboard with PSG Signals",
            showlegend=True
        )

        # Update y-axes
        fig.update_yaxes(title_text="Sleep Stage", tickvals=list(range(5)),
                        ticktext=self.stage_names, row=1, col=1)
        fig.update_yaxes(title_text="Confidence", range=[0, 1], row=2, col=1)
        fig.update_yaxes(title_text="Events", tickvals=[0, 1, 2],
                        ticktext=['LM', 'Apnea', 'Arousal'], row=3, col=1)
        fig.update_yaxes(title_text="Signal Amplitude", row=4, col=1)
        fig.update_yaxes(title_text="Events/Hour", row=5, col=2)

        # Update x-axes
        for row in range(1, 5):  # Include signals row
            fig.update_xaxes(title_text="Time (hours)", row=row, col=1)
        fig.update_xaxes(title_text="Event Type", row=5, col=2)

        # Add instructions for PSG signals
        fig.add_annotation(
            text="PSG Signals: EEG (blue), EOG (green), EMG Chin (red), Airflow (orange)<br>Hover for details, zoom to explore",
            xref="paper", yref="paper",
            x=0.5, y=0.02,
            showarrow=False,
            font=dict(size=10),
            bgcolor="rgba(255,255,255,0.8)",
            bordercolor="black",
            borderwidth=1
        )
        
        # Save interactive plot
        fig.write_html(self.output_dir / f"{save_name}.html")
        fig.show()
        
        return fig
    
    def create_spectral_analysis(self, input_data, sample_labels=None, save_name="spectral_analysis"):
        """Create spectral analysis for different sleep stages"""
        print("Creating spectral analysis...")

        # Handle valid data range
        valid_epochs = None
        if sample_labels is not None and len(sample_labels) > 0:
            sleep_labels = sample_labels[0].squeeze()
            valid_epochs = (sleep_labels != -1).cpu().numpy()
            print(f"Spectral analysis for {np.sum(valid_epochs)} valid epochs")
        
        with torch.no_grad():
            outputs = self.model(input_data)
        
        sleep_output = outputs[0]
        predicted_stages = torch.argmax(torch.softmax(sleep_output, dim=-1), dim=-1)[0].cpu().numpy()

        # Use only valid epochs if available
        if valid_epochs is not None:
            predicted_stages = predicted_stages[valid_epochs]
        
        # Focus on EEG channel for spectral analysis
        eeg_channels = [0]  # Only one EEG channel in model input
        
        fig, axes = plt.subplots(len(self.stage_names), len(eeg_channels), 
                                figsize=(20, 4 * len(self.stage_names)))
        
        # Frequency bands of interest
        freq_bands = {
            'Delta (0.5-4 Hz)': (0.5, 4),
            'Theta (4-8 Hz)': (4, 8),
            'Alpha (8-13 Hz)': (8, 13),
            'Beta (13-30 Hz)': (13, 30),
            'Gamma (30-50 Hz)': (30, 50)
        }
        
        for stage_idx, stage_name in enumerate(self.stage_names):
            stage_mask = predicted_stages == stage_idx
            
            if not np.any(stage_mask):
                continue
            
            # Get epochs for this stage
            stage_epochs = np.where(stage_mask)[0]
            
            for ch_idx, ch in enumerate(eeg_channels):
                if ch >= input_data.shape[1]:
                    continue
                
                ax = axes[stage_idx, ch_idx]
                
                # Collect signal segments for this stage
                stage_signals = []
                for epoch in stage_epochs[:10]:  # Limit to first 10 epochs
                    start_sample = epoch * 30 * self.sampling_rate  # 30-second epochs
                    end_sample = start_sample + 30 * self.sampling_rate
                    
                    if end_sample <= input_data.shape[-1]:
                        segment = input_data[0, ch, start_sample:end_sample].cpu().numpy()
                        stage_signals.append(segment)
                
                if stage_signals:
                    # Compute average power spectral density
                    all_psds = []
                    for segment in stage_signals:
                        freqs, psd = signal.welch(segment, fs=self.sampling_rate, nperseg=512)
                        all_psds.append(psd)
                    
                    avg_psd = np.mean(all_psds, axis=0)
                    
                    # Plot spectrum
                    ax.semilogy(freqs, avg_psd, color=self.stage_colors[stage_idx], linewidth=2)
                    ax.set_xlim(0, 50)
                    ax.set_title(f'{stage_name} - {self.channel_names[ch]}')
                    ax.set_xlabel('Frequency (Hz)')
                    ax.set_ylabel('Power Spectral Density')
                    ax.grid(True, alpha=0.3)
                    
                    # Highlight frequency bands
                    for band_name, (low, high) in freq_bands.items():
                        band_mask = (freqs >= low) & (freqs <= high)
                        if np.any(band_mask):
                            ax.fill_between(freqs[band_mask], 0, avg_psd[band_mask], 
                                          alpha=0.2, label=band_name)
                    
                    if ch_idx == 0:  # Add legend to first column
                        ax.legend(fontsize=8)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / f'{save_name}.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return fig
    
    def create_correlation_analysis(self, input_data, sample_labels=None, save_name="correlation_analysis"):
        """Analyze correlations between PSG channels and sleep events"""
        print("Creating correlation analysis...")

        # Determine valid data range
        valid_seconds = None
        if sample_labels is not None and len(sample_labels) > 0:
            sleep_labels = sample_labels[0].squeeze()
            valid_epochs = (sleep_labels != -1).cpu().numpy()
            valid_seconds = np.sum(valid_epochs) * 30
            print(f"Correlation analysis for {valid_seconds} valid seconds")
        
        with torch.no_grad():
            outputs = self.model(input_data)
        
        # Extract events
        events_data = {}
        event_names = ['Arousal', 'Apnea', 'LM']
        for i, event_name in enumerate(event_names):
            if i + 1 < len(outputs):
                event_output = outputs[i + 1]
                if event_output.shape[-1] == 1:
                    event_probs = torch.sigmoid(event_output).squeeze(-1)[0].cpu().numpy()

                    # Trim to valid seconds if available
                    if valid_seconds is not None:
                        event_probs = event_probs[:valid_seconds]

                    events_data[event_name] = event_probs
        
        # Downsample PSG signals to match event resolution (1 second)
        downsample_factor = self.sampling_rate  # From 100Hz to 1Hz
        n_channels = min(input_data.shape[1], len(self.channel_names))
        
        downsampled_signals = {}
        for ch in range(n_channels):
            signal_data = input_data[0, ch].cpu().numpy()
            # Reshape and average to downsample
            n_seconds = len(signal_data) // downsample_factor
            reshaped = signal_data[:n_seconds * downsample_factor].reshape(n_seconds, downsample_factor)
            downsampled_signals[self.channel_names[ch]] = np.mean(reshaped, axis=1)
        
        # Create correlation matrix
        all_data = {}
        all_data.update(downsampled_signals)
        all_data.update(events_data)
        
        # Ensure all arrays have the same length
        min_length = min(len(data) for data in all_data.values())
        for key in all_data:
            all_data[key] = all_data[key][:min_length]
        
        # Create DataFrame for correlation analysis
        df = pd.DataFrame(all_data)
        correlation_matrix = df.corr()
        
        # Create visualization
        fig, axes = plt.subplots(1, 2, figsize=(20, 8))
        
        # 1. Full correlation heatmap
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,
                   square=True, ax=axes[0], cbar_kws={"shrink": .8})
        axes[0].set_title('PSG Channel and Event Correlations')
        
        # 2. Event-specific correlations
        event_correlations = correlation_matrix[event_names].drop(event_names, axis=0)
        sns.heatmap(event_correlations, annot=True, cmap='coolwarm', center=0,
                   ax=axes[1], cbar_kws={"shrink": .8})
        axes[1].set_title('PSG Channel Correlations with Sleep Events')
        axes[1].set_xlabel('Sleep Events')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / f'{save_name}.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return fig, correlation_matrix

    def create_model_uncertainty_analysis(self, input_data, save_name="uncertainty_analysis"):
        """Analyze model uncertainty and prediction confidence"""
        print("Creating model uncertainty analysis...")

        # Store original training state
        original_training_state = self.model.training

        # Enable dropout for uncertainty estimation if model has dropout layers
        try:
            self.model.train()  # Enable dropout

            n_samples = 20  # Number of forward passes for uncertainty estimation
            all_predictions = []

            with torch.no_grad():
                for _ in range(n_samples):
                    outputs = self.model(input_data)
                    all_predictions.append([output.cpu() for output in outputs])

        except Exception as e:
            print(f"Warning: Could not enable dropout for uncertainty analysis: {e}")
            # Fallback to single prediction
            n_samples = 1
            with torch.no_grad():
                outputs = self.model(input_data)
                all_predictions = [[output.cpu() for output in outputs]]

        finally:
            # Restore original training state
            self.model.train(original_training_state)

        # Analyze uncertainty for each task
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        axes = axes.flatten()

        task_names = ['Sleep', 'Arousal', 'Apnea', 'LM']

        # If we only have one sample (fallback), show confidence instead of uncertainty
        if n_samples == 1:
            print("Showing prediction confidence instead of uncertainty...")
            with torch.no_grad():
                outputs = self.model(input_data)

            for task_idx, task_name in enumerate(task_names):
                if task_idx >= len(axes) or task_idx >= len(outputs):
                    continue

                ax = axes[task_idx]
                task_output = outputs[task_idx]

                if task_output.shape[-1] == 1:
                    # Binary task
                    probs = torch.sigmoid(task_output).squeeze(-1)[0].cpu().numpy()
                    time_axis = np.arange(len(probs)) / 3600

                    ax.plot(time_axis, probs, label='Prediction Probability', linewidth=2)
                    ax.axhline(y=0.5, color='red', linestyle='--', alpha=0.5, label='Decision Threshold')
                    ax.set_ylabel('Probability')
                    ax.set_ylim(0, 1)
                else:
                    # Multi-class task
                    probs = torch.softmax(task_output, dim=-1)[0].cpu().numpy()
                    max_probs = np.max(probs, axis=-1)
                    time_axis = np.arange(len(max_probs)) * 0.5 / 60

                    ax.plot(time_axis, max_probs, linewidth=2)
                    ax.set_ylabel('Max Class Probability')

                ax.set_title(f'{task_name} - Prediction Confidence')
                ax.set_xlabel('Time (hours)')
                ax.legend()
                ax.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.savefig(self.output_dir / f'{save_name}.png', dpi=300, bbox_inches='tight')
            plt.show()
            return fig

        for task_idx, task_name in enumerate(task_names):
            if task_idx >= len(axes) or task_idx >= len(all_predictions[0]):
                continue

            ax = axes[task_idx]

            # Collect predictions for this task
            task_predictions = [pred[task_idx] for pred in all_predictions]

            if task_predictions[0].shape[-1] == 1:
                # Binary task - compute sigmoid
                probs = [torch.sigmoid(pred).squeeze(-1) for pred in task_predictions]
            else:
                # Multi-class task - compute softmax
                probs = [torch.softmax(pred, dim=-1) for pred in task_predictions]

            # Stack predictions
            stacked_probs = torch.stack(probs, dim=0)  # [n_samples, batch, time, classes]

            # Compute mean and uncertainty
            mean_probs = torch.mean(stacked_probs, dim=0)[0]  # [time, classes]
            std_probs = torch.std(stacked_probs, dim=0)[0]    # [time, classes]

            if task_predictions[0].shape[-1] == 1:
                # Binary task
                time_axis = np.arange(len(mean_probs)) / 3600  # Convert to hours

                ax.plot(time_axis, mean_probs.numpy(), label='Mean Prediction', linewidth=2)
                ax.fill_between(time_axis,
                               (mean_probs - std_probs).numpy(),
                               (mean_probs + std_probs).numpy(),
                               alpha=0.3, label='Uncertainty (±1 std)')

                # Highlight high uncertainty regions
                high_uncertainty = std_probs > 0.1
                if torch.any(high_uncertainty):
                    ax.scatter(time_axis[high_uncertainty], mean_probs[high_uncertainty],
                             color='red', s=10, alpha=0.7, label='High Uncertainty')

                ax.set_ylabel('Probability')
                ax.set_ylim(0, 1)

            else:
                # Multi-class task - show uncertainty for predicted class
                predicted_classes = torch.argmax(mean_probs, dim=-1)
                class_uncertainties = std_probs[range(len(predicted_classes)), predicted_classes]

                time_axis = np.arange(len(class_uncertainties)) * 0.5 / 60  # Convert to hours

                ax.plot(time_axis, class_uncertainties.numpy(), linewidth=2)
                ax.fill_between(time_axis, 0, class_uncertainties.numpy(), alpha=0.3)

                # Highlight high uncertainty regions
                high_uncertainty = class_uncertainties > 0.05
                if torch.any(high_uncertainty):
                    ax.scatter(time_axis[high_uncertainty], class_uncertainties[high_uncertainty],
                             color='red', s=10, alpha=0.7, label='High Uncertainty')

                ax.set_ylabel('Prediction Uncertainty')

            ax.set_title(f'{task_name} - Model Uncertainty')
            ax.set_xlabel('Time (hours)')
            ax.legend()
            ax.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(self.output_dir / f'{save_name}.png', dpi=300, bbox_inches='tight')
        plt.show()

        return fig

    def create_clinical_summary_dashboard(self, input_data, sample_labels=None, save_name="clinical_summary"):
        """Create a comprehensive clinical summary dashboard"""
        print("Creating clinical summary dashboard...")

        # Handle valid data range
        valid_epochs = None
        valid_seconds = None
        if sample_labels is not None and len(sample_labels) > 0:
            sleep_labels = sample_labels[0].squeeze()
            valid_epochs = (sleep_labels != -1).cpu().numpy()
            valid_seconds = np.sum(valid_epochs) * 30
            print(f"Clinical summary for {np.sum(valid_epochs)} valid epochs ({valid_seconds/3600:.1f} hours)")

        with torch.no_grad():
            outputs = self.model(input_data)

        # Extract all predictions
        sleep_output = outputs[0]
        stage_probs = torch.softmax(sleep_output, dim=-1)
        predicted_stages = torch.argmax(stage_probs, dim=-1)[0].cpu().numpy()

        # Use only valid epochs if available
        if valid_epochs is not None:
            predicted_stages = predicted_stages[valid_epochs]

        # Extract events
        events_data = {}
        event_names = ['Arousal', 'Apnea', 'LM']
        for i, event_name in enumerate(event_names):
            if i + 1 < len(outputs):
                event_output = outputs[i + 1]
                if event_output.shape[-1] == 1:
                    event_probs = torch.sigmoid(event_output).squeeze(-1)[0].cpu().numpy()
                    event_predictions = event_probs > 0.5

                    # Trim to valid seconds if available
                    if valid_seconds is not None:
                        event_predictions = event_predictions[:valid_seconds]

                    events_data[event_name] = event_predictions

        # Calculate clinical metrics
        total_epochs = len(predicted_stages)
        total_recording_time = total_epochs * 0.5 / 60  # Hours (now based on valid epochs only)

        # Sleep architecture
        stage_counts = [np.sum(predicted_stages == i) for i in range(5)]
        stage_durations = [count * 0.5 for count in stage_counts]  # Minutes
        total_sleep_time = sum(stage_durations[1:])  # Exclude wake
        sleep_efficiency = (total_sleep_time / (total_recording_time * 60)) * 100

        # Event rates
        event_rates = {}
        for event_name, event_data in events_data.items():
            total_events = np.sum(event_data)
            rate_per_hour = total_events / total_recording_time
            event_rates[event_name] = rate_per_hour

        # Create dashboard
        fig = plt.figure(figsize=(20, 16))
        gs = fig.add_gridspec(4, 4, hspace=0.3, wspace=0.3)

        # 1. Sleep Architecture Pie Chart
        ax1 = fig.add_subplot(gs[0, 0])
        wedges, texts, autotexts = ax1.pie(stage_durations, labels=self.stage_names,
                                          colors=self.stage_colors, autopct='%1.1f%%')
        ax1.set_title('Sleep Architecture', fontsize=14, fontweight='bold')

        # 2. Sleep Efficiency Gauge
        ax2 = fig.add_subplot(gs[0, 1])
        theta = np.linspace(0, np.pi, 100)
        r = np.ones_like(theta)

        # Create gauge background
        ax2.plot(theta, r, 'lightgray', linewidth=20)

        # Color code efficiency
        if sleep_efficiency >= 85:
            color = 'green'
            status = 'Normal'
        elif sleep_efficiency >= 70:
            color = 'orange'
            status = 'Mild Impairment'
        else:
            color = 'red'
            status = 'Significant Impairment'

        # Plot efficiency arc
        efficiency_theta = theta[:int(sleep_efficiency)]
        ax2.plot(efficiency_theta, r[:len(efficiency_theta)], color, linewidth=20)

        ax2.set_ylim(0, 1.2)
        ax2.set_xlim(-0.2, np.pi + 0.2)
        ax2.axis('off')
        ax2.text(np.pi/2, 0.5, f'{sleep_efficiency:.1f}%\n{status}',
                ha='center', va='center', fontsize=12, fontweight='bold')
        ax2.set_title('Sleep Efficiency', fontsize=14, fontweight='bold')

        # 3. Event Rates Bar Chart
        ax3 = fig.add_subplot(gs[0, 2:])
        bars = ax3.bar(event_rates.keys(), event_rates.values(),
                      color=['red', 'blue', 'purple'], alpha=0.7)

        # Add clinical thresholds
        ax3.axhline(y=5, color='orange', linestyle='--', alpha=0.7, label='Mild Apnea (5/hr)')
        ax3.axhline(y=15, color='red', linestyle='--', alpha=0.7, label='Moderate Apnea (15/hr)')
        ax3.axhline(y=30, color='darkred', linestyle='--', alpha=0.7, label='Severe Apnea (30/hr)')

        ax3.set_ylabel('Events per Hour')
        ax3.set_title('Sleep Event Rates', fontsize=14, fontweight='bold')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # Add severity labels
        for bar, (event_name, rate) in zip(bars, event_rates.items()):
            height = bar.get_height()
            if event_name == 'Apnea':
                if rate >= 30:
                    severity = 'Severe'
                    text_color = 'darkred'
                elif rate >= 15:
                    severity = 'Moderate'
                    text_color = 'red'
                elif rate >= 5:
                    severity = 'Mild'
                    text_color = 'orange'
                else:
                    severity = 'Normal'
                    text_color = 'green'

                ax3.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                        severity, ha='center', va='bottom',
                        fontweight='bold', color=text_color)

        # 4. Hypnogram
        ax4 = fig.add_subplot(gs[1, :])
        epoch_times = np.arange(len(predicted_stages)) * 0.5 / 60  # Hours

        # Plot sleep stages
        for i, (stage, color) in enumerate(zip(self.stage_names, self.stage_colors)):
            mask = predicted_stages == i
            if np.any(mask):
                ax4.scatter(epoch_times[mask], [i] * np.sum(mask),
                          c=color, s=15, alpha=0.8, label=stage)

        ax4.set_ylabel('Sleep Stage')
        ax4.set_xlabel('Time (hours)')
        ax4.set_yticks(range(5))
        ax4.set_yticklabels(self.stage_names)
        ax4.set_title('Hypnogram', fontsize=14, fontweight='bold')
        ax4.grid(True, alpha=0.3)
        ax4.invert_yaxis()
        ax4.legend(loc='upper right', ncol=5)

        # 5. Clinical Metrics Table
        ax5 = fig.add_subplot(gs[2:, :2])
        ax5.axis('off')

        # Calculate additional metrics
        rem_latency = None
        rem_epochs = np.where(predicted_stages == 4)[0]
        if len(rem_epochs) > 0:
            rem_latency = rem_epochs[0] * 0.5  # Minutes

        # Sleep stage percentages
        sleep_stage_percentages = {}
        if total_sleep_time > 0:
            for i, stage in enumerate(['N1', 'N2', 'N3', 'REM']):
                percentage = (stage_durations[i+1] / total_sleep_time) * 100
                sleep_stage_percentages[stage] = percentage

        # Create metrics table
        metrics_data = [
            ['Total Recording Time', f'{total_recording_time:.1f} hours'],
            ['Total Sleep Time', f'{total_sleep_time:.0f} minutes'],
            ['Sleep Efficiency', f'{sleep_efficiency:.1f}%'],
            ['REM Latency', f'{rem_latency:.0f} minutes' if rem_latency else 'N/A'],
            ['', ''],  # Separator
            ['Sleep Stage Distribution:', ''],
        ]

        for stage, percentage in sleep_stage_percentages.items():
            metrics_data.append([f'  {stage}', f'{percentage:.1f}%'])

        metrics_data.extend([
            ['', ''],  # Separator
            ['Event Rates:', ''],
            [f'  Apnea-Hypopnea Index', f'{event_rates.get("Apnea", 0):.1f}/hour'],
            [f'  Arousal Index', f'{event_rates.get("Arousal", 0):.1f}/hour'],
            [f'  Limb Movement Index', f'{event_rates.get("LM", 0):.1f}/hour'],
        ])

        # Create table
        table = ax5.table(cellText=metrics_data, cellLoc='left', loc='center',
                         colWidths=[0.6, 0.4])
        table.auto_set_font_size(False)
        table.set_fontsize(11)
        table.scale(1, 2)

        # Style the table
        for i, (key, value) in enumerate(metrics_data):
            if key.endswith(':') or key == '':
                table[(i, 0)].set_facecolor('#E8E8E8')
                table[(i, 1)].set_facecolor('#E8E8E8')
                table[(i, 0)].set_text_props(weight='bold')

        ax5.set_title('Clinical Metrics', fontsize=14, fontweight='bold')

        # 6. Clinical Interpretation
        ax6 = fig.add_subplot(gs[2:, 2:])
        ax6.axis('off')

        # Generate clinical interpretation
        interpretation = []

        # Sleep efficiency interpretation
        if sleep_efficiency >= 85:
            interpretation.append("✅ Normal sleep efficiency")
        elif sleep_efficiency >= 70:
            interpretation.append("⚠️ Mildly reduced sleep efficiency")
        else:
            interpretation.append("🔴 Significantly reduced sleep efficiency")

        # Sleep architecture interpretation
        if 'N3' in sleep_stage_percentages and sleep_stage_percentages['N3'] < 15:
            interpretation.append("⚠️ Reduced deep sleep (N3 < 15%)")

        if 'REM' in sleep_stage_percentages and sleep_stage_percentages['REM'] < 20:
            interpretation.append("⚠️ Reduced REM sleep (< 20%)")

        # REM latency interpretation
        if rem_latency and rem_latency < 60:
            interpretation.append("⚠️ Short REM latency (< 60 min)")

        # Event interpretation
        apnea_rate = event_rates.get('Apnea', 0)
        if apnea_rate >= 30:
            interpretation.append("🔴 Severe sleep apnea (AHI ≥ 30)")
        elif apnea_rate >= 15:
            interpretation.append("🟡 Moderate sleep apnea (AHI 15-29)")
        elif apnea_rate >= 5:
            interpretation.append("🟠 Mild sleep apnea (AHI 5-14)")
        else:
            interpretation.append("✅ Normal breathing (AHI < 5)")

        arousal_rate = event_rates.get('Arousal', 0)
        if arousal_rate > 15:
            interpretation.append("⚠️ High arousal index (> 15/hour)")
        else:
            interpretation.append("✅ Normal arousal index")

        # Display interpretation
        interpretation_text = '\n\n'.join(interpretation)
        ax6.text(0.05, 0.95, interpretation_text, transform=ax6.transAxes,
                verticalalignment='top', fontsize=12,
                bbox=dict(boxstyle='round,pad=1', facecolor='lightblue', alpha=0.8))
        ax6.set_title('Clinical Interpretation', fontsize=14, fontweight='bold')

        plt.suptitle('Sleep Study Analysis Report', fontsize=18, fontweight='bold', y=0.98)
        plt.savefig(self.output_dir / f'{save_name}.png', dpi=300, bbox_inches='tight')
        plt.show()

        return fig
