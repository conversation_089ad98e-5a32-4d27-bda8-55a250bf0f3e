import numpy as np
from SleePyPhases import DataManipulation as SPPDataManipulation


class DataManipulation(SPPDataManipulation):
    """
    X: (numSegments, numSamples, numChannels)
    Y: (numSegments, ... )
    """


    def combineY(self, X, Y, values, channel=0):
        smallest = min(values)
        Y[:, :, channel] = np.where(np.isin(Y[:, :, channel], values), values[-1], Y[:, :, channel])
        Y[:, :, channel][Y[:, :, channel] > smallest] -= len(values) - 1
        
        return X, Y
    
    def toLightDeepSleep(self, X, Y, channel=0):
        # [Wake, R, N1, N2, N3] ->  [Wake, R, Light, Deep]
        Y[:, : , channel][Y[:, : , channel] == 3] = 2
        Y[:, : , channel][Y[:, : , channel] == 4] = 3

        return X, Y
    
    def flattenBatch(self, X, Y):
        X = X.reshape(-1, X.shape[-1])
        Y = Y.reshape(-1, Y.shape[-1])

        return X, Y
    
    # stackTensors, changeType, fixedSizeX and _fixeSizeSingleChannel are copied from SleePyPhases implementation 

    def transpose(self, X, Y):    
        num_records = len(X)
        num_labels = len(X[0])
        
        # Create transposed structure
        X = [
            [X[record][label] for record in range(num_records)]
            for label in range(num_labels)
        ]

        num_records = len(Y)
        num_labels = len(Y[0])
        
        # Create transposed structure
        Y = [
            [Y[record][label] for record in range(num_records)]
            for label in range(num_labels)
        ]

        return X, Y
    
    def changeType(self, X, Y, dtype, changeX=True, changeY=True):
        if changeX:
            X = X.astype(dtype)
        if changeY:
            Y = Y.astype(dtype)

        return X, Y

    def fixedSizeX(self, X, Y, size, fillValue=0, position="center", moduloStart=1):
        X, _ = self._fixeSizeSingleChannel(X, size, fillValue, position, moduloStart=moduloStart)
        return X, Y

    def fixedSizeY(self, X, Y, size, fillValue=0, position="center", moduloStart=1):
        Y, _ = self._fixeSizeSingleChannel(Y, size, fillValue, position, moduloStart=moduloStart)
        return X, Y
    
    
    def _fixeSizeSingleChannel(self, X, size, fillValue=0, position="center", startAt=0, moduloStart=1):
        newShape = list(X.shape)
        newShape[1] = size

        centerNew = size // 2
        signalSize = X.shape[1]
        centerSignal = signalSize // 2

        if startAt == 0 and position == "center":
            startAt = centerNew - centerSignal

        newX = np.full(newShape, fillValue, dtype=X.dtype)

        startAt -= startAt % moduloStart
        if startAt < 0:
            offsetStart = startAt * -1
            length = min(newShape[1], X.shape[1] - offsetStart)
            newX[:, :length, :] = X[:, offsetStart : offsetStart + newShape[1], :]
        else:
            newLength = min(startAt + signalSize, size)
            newX[:, startAt : startAt + signalSize, :] = X[:,0:newLength,:]

        return newX, startAt
    
    # batch manipulation

    def prepareBatch(self, X, Y):
        import torch
        if not isinstance(X, list):
            X = [X]
            Y = [Y]
        X = torch.tensor(np.array(X)).reshape(-1, *X[0].shape[1:])
        # Y = [torch.tensor(b) for y in Y for b in y]
        # make batch 2nd dimension
        transposedY = list(zip(*Y))
        Y = [torch.tensor(np.stack(item_group)).permute(0, 2, 1) for item_group in transposedY]
        X = X.permute(0, 2, 1)
        return X, Y
    
    def _fitToReduction(self, Y, factor):
        if Y.shape[1] % factor != 0:
            cutOff = int( Y.shape[1] % factor)
            Y = Y[:, :-cutOff, :]

        return Y
    
    def reduceY(self, X, Y, factor, reduce="mean"):
        """reduce prediction by factor and using the mean of the prediction, using majority for Y"""
        
        Y = self._fitToReduction(Y, factor)
        Y = Y.reshape(Y.shape[0], Y.shape[1] // factor, factor, Y.shape[2])

        if reduce == "mean":
            arr = Y.astype(np.int32) if isinstance(Y, np.ndarray) else Y.int()
            Y = np.apply_along_axis(lambda y: np.bincount(y).argmax(), axis=2, arr=arr)
        elif reduce == "max":
            Y = Y.max(axis=2)

        return X, Y
    
    
    def addBatchDimension(self, X, Y):
        return X[np.newaxis], Y[np.newaxis]
    
    def prepareModelInput(self, X, Y, hours, samplingrate, position, sleepReduce, eventReduce, reduce="mean", moduloStart=1):
        sizeX = samplingrate * 60 * 60 * hours
        sizeSleepY = int(samplingrate / sleepReduce * 60 * 60 * hours)
        sizeEventY = int(eventReduce / eventReduce * 60 * 60 * hours)

        X, _ = self._fixeSizeSingleChannel(X, sizeX, 0, position, moduloStart=moduloStart)

        X, sleepY = self.reduceY(X, Y[:, :, :1], factor=sleepReduce, reduce=reduce)
        X, eventY = self.reduceY(X, Y[:, :, 1:], factor=eventReduce, reduce=reduce)

        sleepY, _ = self._fixeSizeSingleChannel(sleepY, sizeSleepY, -1, position, moduloStart=moduloStart)
        eventY, _ = self._fixeSizeSingleChannel(eventY, sizeEventY, -1, position, moduloStart=moduloStart)

        return X, [
            sleepY[:, :, 0], 
            eventY[:, :, 0], 
            eventY[:, :, 1], 
            eventY[:, :, 2],
        ]
    
    def prepareSingleSleep(self, X, Y, hours, samplingrate, position, sleepReduce, eventReduce, reduce="mean", moduloStart=1):
        sizeX = samplingrate * 60 * 60 * hours
        sizeSleepY = int(samplingrate / sleepReduce * 60 * 60 * hours)

        X, _ = self._fixeSizeSingleChannel(X, sizeX, 0, position, moduloStart=moduloStart)

        X, sleepY = self.reduceY(X, Y[:, :, :1], factor=sleepReduce, reduce=reduce)

        sleepY, _ = self._fixeSizeSingleChannel(sleepY, sizeSleepY, -1, position, moduloStart=moduloStart)

        return X, [
            sleepY[:, :, 0], 
        ]
    
    
    def prepareSingleEvent(self, X, Y, hours, samplingrate, position, eventReduce, reduce="mean", moduloStart=1):
        sizeX = samplingrate * 60 * 60 * hours
        sizeEventY = int(eventReduce / eventReduce * 60 * 60 * hours)

        X, _ = self._fixeSizeSingleChannel(X, sizeX, 0, position, moduloStart=moduloStart)

        X, eventY = self.reduceY(X, Y, factor=eventReduce, reduce=reduce)

        eventY, _ = self._fixeSizeSingleChannel(eventY, sizeEventY, -1, position, moduloStart=moduloStart)

        return X, [
            eventY[:, :, 0], 
        ]
    
    def ignoreWakeSegments(self, X, Y, channel, sleepChannel=0):
        Y_copy = Y.copy()
        whereWake = Y_copy[:, :, sleepChannel] <= 0
        Y_copy[whereWake, channel] = -1
        return X, Y_copy